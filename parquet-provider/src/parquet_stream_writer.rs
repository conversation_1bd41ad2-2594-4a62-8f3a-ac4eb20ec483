use crate::parquet_provider::ParquetProviderError;
use crate::RecordBatchWrapper;
use arrow::record_batch::RecordBatch;
use ck_provider::{CkProviderError, StreamWriter};
use parquet::arrow::ArrowWriter;
use parquet::basic::{Compression, ZstdLevel};
use parquet::file::properties::WriterProperties;
use std::fs::File;
use std::path::Path;
use uuid::Uuid;

// Error conversion for compatibility with generic stream processor
impl From<ParquetProviderError> for CkProviderError {
    fn from(err: ParquetProviderError) -> Self {
        CkProviderError::RuntimeError(err.to_string())
    }
}

/// Parquet stream writer that implements the StreamWriter trait
pub struct ParquetStreamWriter {
    writer: Option<ArrowWriter<File>>,
    target_file_path: String,
    batch_size: usize,
    total_rows_written: usize,
}

impl ParquetStreamWriter {
    /// Create a new ParquetStreamWriter
    pub fn new(target_file_path: String, batch_size: usize) -> Result<Self, ParquetProviderError> {
        Ok(Self {
            writer: None,
            target_file_path,
            batch_size,
            total_rows_written: 0,
        })
    }

    /// Initialize the writer with the first batch to get schema
    fn initialize_writer(&mut self, first_batch: &RecordBatch) -> Result<(), ParquetProviderError> {
        if self.writer.is_some() {
            return Ok(()); // Already initialized
        }

        let schema = first_batch.schema();
        let file = File::create(&self.target_file_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!(
                "Failed to create temp file {}: {}",
                self.target_file_path, e
            ))
        })?;

        let props = WriterProperties::builder()
            .set_compression(Compression::ZSTD(ZstdLevel::default()))
            .set_dictionary_enabled(true)
            .set_max_row_group_size(1 * 1024 * 1024)
            .build();

        let writer = ArrowWriter::try_new(file, schema, Some(props))
            .map_err(|e| ParquetProviderError::ParquetError(e))?;

        self.writer = Some(writer);
        log::info!("Initialized ParquetStreamWriter for file: {}", self.target_file_path);

        Ok(())
    }

    /// Convert generic data to RecordBatch if needed
    fn convert_to_record_batch<T>(&self, batch: Vec<T>) -> Result<RecordBatch, ParquetProviderError>
    where
        T: RecordBatchWrapper,
    {
        if batch.is_empty() {
            return Err(ParquetProviderError::EmptyDataError(
                "Cannot convert empty batch to RecordBatch".to_string(),
            ));
        }

        T::to_record_batch(&batch).map_err(|e| {
            ParquetProviderError::DataConversionError(format!(
                "Failed to convert data to RecordBatch: {}",
                e
            ))
        })
    }
}

impl<T> StreamWriter<T> for ParquetStreamWriter
where
    T: RecordBatchWrapper + Send + Sync,
{
    type Error = ParquetProviderError;

    async fn write_batch(&mut self, batch: Vec<T>) -> Result<(), Self::Error> {
        if batch.is_empty() {
            return Ok(());
        }

        // Convert to RecordBatch
        let record_batch = self.convert_to_record_batch(batch)?;

        // Initialize writer if this is the first batch
        self.initialize_writer(&record_batch)?;

        // Get the writer (we know it exists now)
        let writer = self.writer.as_mut().unwrap();

        // Write the batch
        writer.write(&record_batch).map_err(|e| ParquetProviderError::ParquetError(e))?;

        self.total_rows_written += record_batch.num_rows();
        log::debug!(
            "Wrote batch with {} rows to parquet stream, total rows: {}",
            record_batch.num_rows(),
            self.total_rows_written
        );

        Ok(())
    }

    async fn flush(&mut self) -> Result<(), Self::Error> {
        if let Some(writer) = &mut self.writer {
            writer.flush().map_err(|e| ParquetProviderError::ParquetError(e))?;
            log::debug!("Flushed parquet stream writer");
        }
        Ok(())
    }

    async fn close(mut self) -> Result<(), Self::Error> {
        if let Some(writer) = self.writer.take() {
            writer.close().map_err(|e| ParquetProviderError::ParquetError(e))?;
            log::info!(
                "Closed parquet stream writer, total rows written: {}",
                self.total_rows_written
            );
        }

        Ok(())
    }
}

/// Alternative implementation for RecordBatch directly
pub struct RecordBatchStreamWriter {
    writer: Option<ArrowWriter<File>>,
    target_file_path: String,
    total_rows_written: usize,
}

impl RecordBatchStreamWriter {
    /// Create a new RecordBatchStreamWriter
    pub fn new(target_file_path: String) -> Result<Self, ParquetProviderError> {

        Ok(Self {
            writer: None,
            target_file_path,
            total_rows_written: 0,
        })
    }

    /// Initialize the writer with the first batch to get schema
    fn initialize_writer(&mut self, first_batch: &RecordBatch) -> Result<(), ParquetProviderError> {
        if self.writer.is_some() {
            return Ok(()); // Already initialized
        }

        let schema = first_batch.schema();
        let file = File::create(&self.target_file_path).map_err(|e| {
            ParquetProviderError::FileOperationError(format!(
                "Failed to create temp file {}: {}",
                self.target_file_path, e
            ))
        })?;

        let props = WriterProperties::builder()
            .set_compression(Compression::ZSTD(ZstdLevel::default()))
            .set_dictionary_enabled(true)
            .set_max_row_group_size(1 * 1024 * 1024)
            .build();

        let writer = ArrowWriter::try_new(file, schema, Some(props))
            .map_err(|e| ParquetProviderError::ParquetError(e))?;

        self.writer = Some(writer);
        log::info!("Initialized RecordBatchStreamWriter for file: {}", self.target_file_path);

        Ok(())
    }
}

impl StreamWriter<RecordBatch> for RecordBatchStreamWriter {
    type Error = ParquetProviderError;

    async fn write_batch(&mut self, batch: Vec<RecordBatch>) -> Result<(), Self::Error> {
        if batch.is_empty() {
            return Ok(());
        }

        for record_batch in batch {
            if record_batch.num_rows() == 0 {
                continue;
            }

            // Initialize writer if this is the first batch
            self.initialize_writer(&record_batch)?;

            // Get the writer (we know it exists now)
            let writer = self.writer.as_mut().unwrap();

            log::info!("开始写入RecordBatch num_rows() = {}", record_batch.num_rows());
            // Write the batch
            writer.write(&record_batch).map_err(|e| ParquetProviderError::ParquetError(e))?;

            self.total_rows_written += record_batch.num_rows();
            log::info!(
                "Wrote RecordBatch to parquet stream, total rows: {}", self.total_rows_written
            );
        }

        Ok(())
    }

    async fn flush(&mut self) -> Result<(), Self::Error> {
        if let Some(writer) = &mut self.writer {
            writer.flush().map_err(|e| ParquetProviderError::ParquetError(e))?;
            log::debug!("Flushed RecordBatch stream writer");
        }
        Ok(())
    }

    async fn close(mut self) -> Result<(), Self::Error> {
        if let Some(writer) = self.writer.take() {
            writer.close().map_err(|e| ParquetProviderError::ParquetError(e))?;
            log::info!(
                "Closed RecordBatch stream writer, total rows written: {}",
                self.total_rows_written
            );
        }

        Ok(())
    }
}
