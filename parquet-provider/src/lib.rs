pub mod constant;
pub mod parquet_provider;
pub mod hdfs_provider;
pub mod parquet_stream_writer;

use arrow::array::RecordBatch;
pub use parquet_stream_writer::{ParquetStreamWriter, RecordBatchStreamWriter};

pub trait RecordBatchWrapper {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized;

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized;
}
