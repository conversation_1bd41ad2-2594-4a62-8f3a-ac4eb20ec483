[package]
name = "parquet-provider"
version = "0.1.0"
edition = "2021"

[dependencies]
log = { workspace = true }
arrow = { workspace = true }
parquet = { workspace = true }
ck-provider = { path = "../ck-provider" }
env_logger = { workspace = true }
tokio = { version = "1.0", features = ["full"] }
uuid = { workspace = true, features = ["v4"] }
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
color-eyre = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
opendal = { version = "0.54.0", features = ["services-hdfs"] }
walkdir = "2.4.0"
rayon = { workspace = true }
