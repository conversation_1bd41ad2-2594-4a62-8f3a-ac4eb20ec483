use color_eyre::{eyre::<PERSON>rap<PERSON>rr, Result};
use dw_test_item::task::cp_task_params::CpTaskParams;
use dw_test_item::task::cp_test_item_task::CpTestItemTask;

use dw_test_item::task::ft_task_params::FtTaskParams;
use dw_test_item::task::ft_test_item_task::FtTestItemTask;
use time::macros::format_description;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;
use tracing_subscriber::fmt::time::LocalTime;

#[tokio::main]
async fn main() -> Result<()> {
    unsafe {
        std::env::set_var("RUST_BACKTRACE", "full");
    }
    color_eyre::install()?;
    let format = format_description!("[year repr:last_two]/[month]/[day] [hour]:[minute]:[second]");
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_timer(LocalTime::new(format))
        .with_file(true)
        .with_line_number(true)
        .init();
    execute_cp_test_item_task().await?;
    Ok(())
}

async fn execute_cp_test_item_task() -> Result<()> {
    let task = CpTestItemTask::new();

    task.do_task(CpTaskParams::new(
        r#"{"customer": "YEESTOR",
        "subCustomer": "YEESTOR",
        "factory": "LEADYO",
        "factorySite": "LEADYO",
        "testArea": "CP",
        "lotId": "ENF083",
        "waferNo": "25",
        "lotType": "PRODUCTION",
        "deviceId": "YS8293ENAB",
        "testStage": "CP1",
        "executeMode": "AUTO",
        "fileCategory": "STDF",
        "ckSinkType": "JDBC",
        "hdfsResultPartition": "1",
        "dataVersion": "1",
        "calculateYmsTestItem": "1"}
        "#,
    )?)
    .await
    .wrap_err("执行CP测试项任务失败")?;
    Ok(())
}

async fn execute_ft_test_item_task() -> Result<()> {
    let task = FtTestItemTask::new();

    task.do_task(FtTaskParams::new(
        r#"{"factory":"JSCC",
        "executeMode":"AUTO",
        "testArea":"FT",
        "ckSinkType":"JDBC",
        "subCustomer":"KUNLUNXIN",
        "dataVersion":"1754648718263",
        "calculateYmsTestItem":"1",
        "hdfsResultPartition":"6",
        "submitMode":"RESIDENT",
        "lotId":"P4M328H",
        "deviceId":"PASA3S3CA0CD",
        "version":"1754648778014",
        "dieCount":"75",
        "factorySite":"JSCC",
        "waferNo":"",
        "newDataFlag":"true",
        "testStage":"FT1",
        "cores":"12",
        "fileCategory":"STDF",
        "lotType":"PRODUCTION",
        "customer":"KUNLUNXIN"}
        "#,
    )?)
    .await
    .wrap_err("执行Ft测试项任务失败")?;
    Ok(())
}
