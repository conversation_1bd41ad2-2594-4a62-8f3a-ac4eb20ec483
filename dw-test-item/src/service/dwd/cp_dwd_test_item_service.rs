use crate::config::DwTestItemConfig;

use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::dwd_service::DwdService;
use common::dwd::model::value::die_test_info::DieTestInfo;
use common::dwd::table::distributed::test_item_detail_service::TestItemDetailService;
use common::model::constant::dw_layer::DwLayer;
use common::model::constant::upload_type::UploadType;
use common::model::dw_table_calculate_step::DwTableCalculateStep;
use common::model::dw_table_enum::DwTableEnum;
use common::model::table_calculate_info::TableCalculateInfo;
use common::model::key::{die_key::Die<PERSON><PERSON>, wafer_key::Wafer<PERSON>ey};
use common::repository::mysql::test_num_force_zero_config_repository::TestNumForceZeroConfigRepository;
use common::repository::mysql::lot_stocking_detail_repository::LotStockingDetailRepository;
use common::repository::mysql::lot_wafer_write_table_repository::LotWaferWriteTableRepository;
use common::service::dw_table_calculate_record_service::DwTableCalculateRecordService;
use common::utils::path;
use mysql_provider::{MySqlConfig, MySqlProviderImpl};
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::Instant;
use crate::service::dwd::dwd_service;
// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/dwd/service/impl/CpDwdTestItemService.scala

/// CpDwdTestItemService handles CP (Contact Probe) stage DWD layer test item processing
/// This service orchestrates the calculation of test item details for the CP test stage
#[derive(Debug, Clone)]
pub struct CpDwdTestItemService {
    test_item_detail_result_partition: i32,
    test_item_detail_service: TestItemDetailService,
    mysql_config: MySqlConfig,
    properties: DwTestItemConfig,
}

/// Calculation result containing broadcast maps and datasets
/// Corresponds to the return type of calculate method in Scala (line 30)
#[derive(Debug)]
pub struct CpDwdCalculationResult {
    pub file_detail_map: HashMap<i64, FileDetail>,
    pub die_detail: Vec<DieDetailParquet>,
    pub test_item_detail: Vec<Vec<SubTestItemDetail>>,
}

impl CpDwdTestItemService {
    /// Create new CpDwdTestItemService
    ///
    /// Corresponds to: CpDwdTestItemService.scala:27 (case class constructor)
    /// case class CpDwdTestItemService(properties: DwTestItemProperties, testItemDetailResultPartition: Int)
    pub fn new(test_item_detail_result_partition: i32, test_area: String) -> Self {
        let config: DwTestItemConfig = DwTestItemConfig::get_config().unwrap();
        Self {
            test_item_detail_result_partition,
            test_item_detail_service: TestItemDetailService::new(test_area),
            mysql_config: config.get_mysql_config(),
            properties: config,
        }
    }

    /// Calculate CP DWD test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:30-90
    /// def calculate(spark: SparkSession, dieDetailSource: Dataset[DieDetail], testItemData: Dataset[TestItemData],
    ///              waferKey: WaferKey, testArea: String, executeMode: String, fileCategory: String,
    ///              ckSinkType: String, runMode: String): (Broadcast[Map[lang.Long, FileDetail]], Dataset[DieDetail], Dataset[SubTestItemDetail])
    pub async fn calculate(
        &self,
        die_detail_source: Vec<DieDetailParquet>,
        test_item_data: Vec<Vec<TestItemDataParquet>>,
        wafer_key: &WaferKey,
        test_area: &String,
        execute_mode: &String,
        file_category: &String,
        _ck_sink_type: &String,
        run_mode: &String,
        config: &DwTestItemConfig,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<CpDwdCalculationResult, Box<dyn Error + Send + Sync>> {
        log::info!("当前正在计算的waferNo: {}", wafer_key);
        let die_detail = die_detail_source;

        let mysql_config = config.get_mysql_config();
        let mut calculate_time_map: HashMap<DwTableEnum, TableCalculateInfo> = HashMap::new();
        let table_calculate_record_service = DwTableCalculateRecordService::new(
            UploadType::AUTO,
            None,
            String::new(),
        );
        let test_num_force_zero_test_program_list = TestNumForceZeroConfigRepository::new(mysql_provider)
            .await?
            .read_test_num_force_zero_test_program_list(
                wafer_key.customer.clone(),
                wafer_key.sub_customer.clone(),
                wafer_key.factory.clone(),
                wafer_key.factory_site.clone(),
                test_area.to_string(),
                wafer_key.device_id.clone(),
                wafer_key.test_stage.clone(),
                die_detail
                    .iter()
                    .map(|die| die.TEST_PROGRAM.clone().unwrap())
                    .collect::<Vec<String>>(),
                UploadType::AUTO.to_string(),
            )
            .await?;

        let file_use_bin_definition_map = LotStockingDetailRepository::new(mysql_provider)
            .await?
            .query_bin_definition_flag(
                &wafer_key.customer,
                &wafer_key.factory,
                &wafer_key.factory_site,
                &wafer_key.test_area,
                &wafer_key.device_id,
                &wafer_key.lot_id,
                &wafer_key.wafer_no,
                &wafer_key.test_stage,
                &wafer_key.lot_type,
                file_category,
            )
            .await?;

        let die_test_info_map = Arc::new(self.build_die_test_info_broadcast_map(&die_detail)?);
        let file_detail_map = self.build_file_detail_broadcast_map(&die_detail)?;

        // 开始计算 DWD_TEST_ITEM_DETAIL
        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DwdTestItemDetail,
            DwTableCalculateStep::CalculateStart,
            Some(config.dwd_db_name.clone()),
        );

        // 使用rayon并行处理每个Vec<TestItemDataParquet>
        let service = Arc::new(self.test_item_detail_service.clone());
        let file_category = Arc::new(file_category.clone());
        let need_multiply_scale = self.properties.get_need_multiply_scale(wafer_key.customer.as_str());
        let standard_units = Arc::new(self.properties.standard_units.clone());
        let need_clear_invalid_data = self.properties.get_clear_invalid_data_flag(wafer_key.customer.as_str());
        let test_num_force_zero_list = Arc::new(test_num_force_zero_test_program_list.clone());
        let file_use_bin_definition_map_arc = Arc::new(file_use_bin_definition_map);

        let test_item_detail: Result<Vec<Vec<SubTestItemDetail>>, Box<dyn Error + Send + Sync>> = test_item_data
            .into_par_iter()
            .enumerate()
            .map(|(index, data_batch)| {
                log::info!("开始处理第{}批数据，数据量: {}", index, data_batch.len());
                let result = service
                    .calculate_cp_test_item_detail(
                        data_batch,
                        &file_category,
                        &die_test_info_map,
                        need_multiply_scale,
                        &standard_units,
                        need_clear_invalid_data,
                        &test_num_force_zero_list,
                        &file_use_bin_definition_map_arc,
                    )
                    .map_err(|e| -> Box<dyn Error + Send + Sync> {
                        Box::new(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("批次{}处理失败: {}", index, e),
                        ))
                    });
                log::info!("完成处理第{}批数据", index);
                result
            })
            .collect();

        let test_item_detail = test_item_detail?;

        log::info!("test_item_detail length: {:?}", test_item_detail.len());

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DwdTestItemDetail,
            DwTableCalculateStep::CalculateEnd,
            Some(config.dwd_db_name.clone()),
        );

        let test_item_detail_path = path::get_dwd_wafer_path(
            &self.properties.cp_test_item_detail_result_dir,
            &test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        // 写入开始记录到 dw_lot_wafer_write_table_record (对应 Scala line 69)
        let lot_wafer_write_table_repository = LotWaferWriteTableRepository::new(mysql_provider).await?;
        lot_wafer_write_table_repository
            .write_record(
                &wafer_key.customer,
                &wafer_key.factory,
                test_area,
                &wafer_key.device_id,
                &wafer_key.lot_id,
                &wafer_key.wafer_no,
                &wafer_key.test_stage,
                &wafer_key.lot_type,
                file_category.as_str(),
                &wafer_key.sub_customer,
                &wafer_key.factory_site,
                &DwTableEnum::DwdTestItemDetail.get_table(),
                0i64,
                0,
            )
            .await
            .map_err(|e| {
                Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("写入开始记录失败: {}", e),
                ))
            })?;

        log::info!("开始同时写入Parquet文件和ClickHouse，路径: {}", test_item_detail_path);

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DwdTestItemDetail,
            DwTableCalculateStep::SinkCkStart,
            Some(config.dwd_db_name.clone()),
        );

        let start_time = Instant::now();
        // 同时写入Parquet和ClickHouse
        let (test_item_detail, file_detail_map) =
            dwd_service::write_test_item_detail_concurrent(
                &test_item_detail_path,
                test_item_detail,
                file_detail_map,
                config,
                Some(&self.properties.get_hdfs_config()),
                50000
            )
                .await?;
        log::info!("成功完成Parquet和ClickHouse的并发写入, 耗时: {}ms", start_time.elapsed().as_millis());


        // 写入完成记录到 dw_lot_wafer_write_table_record (对应 Scala line 88)
        lot_wafer_write_table_repository
            .write_record(
                &wafer_key.customer,
                &wafer_key.factory,
                test_area,
                &wafer_key.device_id,
                &wafer_key.lot_id,
                &wafer_key.wafer_no,
                &wafer_key.test_stage,
                &wafer_key.lot_type,
                file_category.as_str(),
                &wafer_key.sub_customer,
                &wafer_key.factory_site,
                &DwTableEnum::DwdTestItemDetail.get_table(),
                0i64,
                1,
            )
            .await
            .map_err(|e| {
                Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("写入完成记录失败: {}", e),
                ))
            })?;

        table_calculate_record_service.update_dw_table_calculate(
            &mut calculate_time_map,
            DwTableEnum::DwdTestItemDetail,
            DwTableCalculateStep::SinkCkEnd,
            Some(config.dwd_db_name.clone()),
        );

        // 保存表计算记录(耗时、资源)
        table_calculate_record_service.save_dw_table_calculate(
            &wafer_key.customer,
            &wafer_key.sub_customer,
            &wafer_key.factory,
            &wafer_key.factory_site,
            test_area,
            &wafer_key.device_id,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            file_category.as_str(),
            &wafer_key.lot_type,
            &wafer_key.test_stage,
            execute_mode,
            mysql_config,
            &calculate_time_map,
            DwLayer::DWD,
            run_mode,
            Some(UploadType::AUTO),
            mysql_provider,
        ).await?;

        Ok(CpDwdCalculationResult { file_detail_map, die_detail, test_item_detail })
    }

    /// Build die test info map from die details for broadcasting
    /// This method creates the broadcast map used in the calculation
    ///
    /// Corresponds to: CpDwdTestItemService.scala:43
    /// val dieTestInfoMap = sc.broadcast(dieDetail.map(buildDieTestInfo).rdd.distinct().collect.toMap)
    fn build_die_test_info_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<DieKey, DieTestInfo>, Box<dyn Error + Send + Sync>> {
        DwdService::build_die_test_info_broadcast_map(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Build file detail broadcast map from die details using FileDetailService
    /// This method creates the file detail map used for merging with test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:45
    /// val fileDetailMap = FileDetailService().broadcastFileDetail(spark, dieDetail)
    fn build_file_detail_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<i64, FileDetail>, Box<dyn Error + Send + Sync>> {
        let file_detail_service = common::dto::dwd::file_detail::FileDetailService::new();
        file_detail_service.broadcast_file_detail(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Get test item detail result partition count
    pub fn get_test_item_detail_result_partition(&self) -> i32 {
        self.test_item_detail_result_partition
    }
}
