use dashmap::DashMap;
use rayon::prelude::*;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use crate::config::DwTestItemConfig;
use common::dto::dim::{DimTestItem, TestItemKey};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::utils::date;

/// TestItemService handles DIM layer TestItem processing
/// Corresponds to TestItemService.scala
#[derive(Debug, Clone)]
pub struct TestItemService {
    properties: DwTestItemConfig,
}

impl TestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate TestItem from SubTestItemDetail
    /// Corresponds to calculateTestItem method in Scala
    pub async fn calculate_test_item(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        is_cp: bool,
    ) -> Result<Vec<DimTestItem>, Box<dyn std::error::Error + Send + Sync>> {
        log::info!("开始计算 DIM TestItem...");

        // Use DashMap for concurrent grouping and reducing
        let grouped: DashMap<TestItemKey, Mutex<DimTestItem>> = DashMap::new();

        // Transform and group in one pass - stream processing
        sub_test_item
            .par_iter()
            .flatten()
            .filter_map(|elem| {
                if let Some(file_detail) = file_detail_map.get(&elem.FILE_ID?) {
                    Some(DimTestItem::build_test_item(elem, file_detail, is_cp))
                } else {
                    log::warn!("File detail not found for FILE_ID: {:?}", elem.FILE_ID);
                    None
                }
            })
            .for_each(|item| {
                let key = TestItemKey::from_dim_test_item(&item);
                grouped
                    .entry(key)
                    .and_modify(|existing| {
                        let mut existing_item = existing.lock().unwrap();
                        self.calculate_test_item_ts(&mut existing_item, &item);
                    })
                    .or_insert_with(|| Mutex::new(item));
            });

        log::info!("dim test_items 合并完成.");

        // Extract final results and calculate time keys
        let result: Vec<DimTestItem> = grouped
            .into_iter()
            .map(|(_key, mutex_item)| {
                let mut item = mutex_item.into_inner().unwrap();
                // 添加time key
                if let Some(start_time) = item.START_TIME {
                    let start_dt = chrono::DateTime::from_timestamp_millis(start_time).unwrap_or_default();
                    item.START_HOUR_KEY = date::get_day_hour(start_dt).into();
                    item.START_DAY_KEY = date::get_day(start_dt).into();
                }
                if let Some(end_time) = item.END_TIME {
                    let end_dt = chrono::DateTime::from_timestamp_millis(end_time).unwrap_or_default();
                    item.END_HOUR_KEY = date::get_day_hour(end_dt).into();
                    item.END_DAY_KEY = date::get_day(end_dt).into();
                }
                item
            })
            .collect();

        let rows_count = result.len();
        log::info!("DIM TestItem 计算完成，共处理 {} 条记录", rows_count);
        Ok(result)
    }



    /// Calculate time for DimTestItem
    /// Corresponds to calculateTestItemTs method in Scala
    fn calculate_test_item_ts(&self, accumulate: &mut DimTestItem, right: &DimTestItem) {
        // Calculate time - take min start time and max end time
        if let (Some(acc_start), Some(right_start)) = (accumulate.START_TIME, right.START_TIME) {
            accumulate.START_TIME = Some(acc_start.min(right_start));
        } else if right.START_TIME.is_some() {
            accumulate.START_TIME = right.START_TIME;
        }

        if let (Some(acc_end), Some(right_end)) = (accumulate.END_TIME, right.END_TIME) {
            accumulate.END_TIME = Some(acc_end.max(right_end));
        } else if right.END_TIME.is_some() {
            accumulate.END_TIME = right.END_TIME;
        }
    }
}
