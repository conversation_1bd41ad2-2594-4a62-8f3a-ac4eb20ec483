use crate::config::DwTestItemConfig;
use crate::service::ads::{execute_tombstone_operations, AdsYmsTestItemService};
use chrono::Utc;
use ck_provider::{<PERSON>k<PERSON>rovider, CkProviderImpl};
use common::ads::sink::test_item_bin_handler::TestItemBinHandler;
use common::ads::sink::test_item_program_handler::TestItemProgramHandler;
use common::ads::sink::test_item_site_bin_handler::TestItemSiteBinHandler;
use common::ads::sink::test_item_site_handler::TestItemSiteHandler;
use common::ck::ck_sink::SinkHandler;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::test_area::TestArea;
use common::model::constant::*;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;
use std::error::Error;
use std::time::{SystemTime, UNIX_EPOCH};

/// Version flag structure for MySQL query result
/// Corresponds to the VersionFlag case class in Scala
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct VersionFlag {
    pub version_flag: bool,
}

/// FT YMS ADS Test Item Service
/// Handles FT (Final Test) stage ADS layer test item processing for YMS
///
/// Corresponds to: FtYmsAdsTestItemService.scala
/// case class FtYmsAdsTestItemService(properties: DwTestItemProperties) extends YmsAdsTestItemCommonService(properties)
#[derive(Debug, Clone)]
pub struct FtYmsAdsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
}

impl FtYmsAdsTestItemService {
    /// Get product configuration SQL query
    ///
    /// Corresponds to: GET_PRODUCT_SQL in FtYmsAdsTestItemService.scala
    fn get_product_sql(&self) -> String {
        r#"
        SELECT DISTINCT
                ifNull(DATA_SOURCE, '')
               ,ifNull(CUSTOMER, '')
               ,ifNull(SUB_CUSTOMER, '')
               ,ifNull(FACTORY, '')
               ,ifNull(FACTORY_SITE, '')
               ,ifNull(TEST_AREA, '')
               ,ifNull(TEST_STAGE, '')
               ,ifNull(DEVICE_ID, '')
               ,ifNull(PRODUCT, '')
               ,ifNull(PRODUCT_TYPE, '')
               ,ifNull(PRODUCT_FAMILY, '')
        FROM {ODS_DB_NAME}.ods_yms_wafermap_config_snapshot_cluster
        WHERE DATA_SOURCE = '{DATA_SOURCE}'
          AND CUSTOMER = '{CUSTOMER}'
          AND FACTORY = '{FACTORY}'
          AND TEST_AREA = '{TEST_AREA}'
          AND TEST_STAGE = '{TEST_STAGE}'
          AND DEVICE_ID = '{DEVICE_ID}'
          AND DT = (SELECT LATEST_PARTITION_VALUE
                    FROM {META_DB_NAME}.meta_table_latest_partition_cluster
                    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'
                    AND DATABASE_NAME = '{ODS_DB_NAME}')
        "#
        .to_string()
    }

    /// Create new FtYmsAdsTestItemService
    ///
    /// Corresponds to: FtYmsAdsTestItemService.scala case class constructor
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate FT YMS ADS test item processing
    ///
    /// Corresponds to: FtYmsAdsTestItemService.scala:calculate method
    /// def calculate(spark: SparkSession, fileDetailMap: Broadcast[Map[java.lang.Long, FileDetail]],
    ///              SubTestItemDetail: Dataset[SubTestItemDetail], customer: String, factory: String,
    ///              testArea: String, deviceId: String, lotType: String, testStage: String,
    ///              lotId: String, waferNo: String, dataVersion: String, fileCategory: String): Unit
    pub async fn calculate(
        &self,
        file_detail_map: &HashMap<i64, FileDetail>,
        sub_test_item_detail: &Vec<Vec<SubTestItemDetail>>,
        customer: &str,
        factory: &str,
        test_area: &str,
        device_id: &str,
        lot_type: &str,
        test_stage: &str,
        lot_id: &str,
        wafer_no: &str,
        _data_version: &str,
        _file_category: &str,
        calculate_yms_test_item: &str,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        // FT工艺的data_source逻辑：FT工艺通常使用TEST_RAW_DATA
        // 基于代码分析，FT工艺不像CP有复杂的Map数据源判断
        let data_source = TEST_RAW_DATA;

        let handlers: Vec<Box<dyn SinkHandler>> = vec![
            Box::new(TestItemSiteBinHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemBinHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemSiteHandler::new(self.properties.ads_db_name.clone())),
            Box::new(TestItemProgramHandler::new(self.properties.ads_db_name.clone())),
        ];

        execute_tombstone_operations(
            &self.properties,
            &handlers,
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            wafer_no,
            data_source,
            Utc::now(),
        )
        .await?;

        // Create ClickHouse configuration
        let ck_config = self.properties.get_ck_config(self.properties.ads_db_name.as_str());
        let ck_provider = CkProviderImpl::new(ck_config.clone());

        let product_sql = self
            .get_product_sql()
            .replace(ODS_DB_NAME, &self.properties.ods_db_name)
            .replace(META_DB_NAME, &self.properties.meta_db_name)
            .replace(DATA_SOURCE, &data_source)
            .replace(CUSTOMER, customer)
            .replace(FACTORY, factory)
            .replace(TEST_AREA, test_area)
            .replace(TEST_STAGE, test_stage)
            .replace(DEVICE_ID, device_id);

        let product_infos = ck_provider.query::<OdsProductConfig>(&product_sql).await?;

        // Get current timestamp as version
        let version = SystemTime::now().duration_since(UNIX_EPOCH)?.as_millis() as i64;

        // Create ADS YMS Test Item Service
        if calculate_yms_test_item == ZERO {
            log::info!("calculateYmsTestItem is {}, skip calculate yms ads test item", calculate_yms_test_item);
        } else {
            log::info!("calculateYmsTestItem is {}, start calculate yms ads test item", calculate_yms_test_item);
            AdsYmsTestItemService::new(
                self.properties.clone(),
                data_source.to_owned(),
                TestArea::of(test_area)
                    .ok_or_else(|| panic!("Unknown test area: {}", test_area))
                    .unwrap(),
            )
            .calculate_test_item(&sub_test_item_detail, &file_detail_map, &product_infos, version)
            .await?;
        }

        Ok(())
    }
}
