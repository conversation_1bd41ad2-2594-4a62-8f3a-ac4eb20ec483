//! DWS服务通用方法
//!
//! 提供DWS相关的通用操作方法

use crate::config::DwTestItemConfig;
use chrono::Utc;
use common::ck::ck_operate::CkOperate;
use common::ck::ck_sink::Sink<PERSON>andler;
use common::model::key::wafer_key::WaferKey;
use log::{error, info};
use std::error::Error;

/// DWS服务通用方法
pub struct DwsTestItemService;

impl DwsTestItemService {
    /// 通用的tombstone删除方法
    pub async fn execute_tombstone_operation<T>(
        handler: &T,
        wafer_key: &WaferKey,
        test_area: &str,
        partition: &str,
        properties: DwTestItemConfig,
    ) -> Result<(), Box<dyn Error>>
    where
        T: SinkHandler,
    {
        let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());
        let current_time = Utc::now();

        info!("执行tombstone操作，表: {}, 分区: {}", table_full_name, partition);

        CkOperate::tombstone_ck(
            &wafer_key.customer,
            &wafer_key.factory,
            test_area,
            &wafer_key.lot_id,
            &wafer_key.lot_type,
            &wafer_key.test_stage,
            &wafer_key.device_id,
            None, // lot_bucket
            &wafer_key.wafer_no,
            &table_full_name,
            &properties.pick_random_ck_node_host(),
            &properties.get_ck_address_list(),
            &properties.ck_username,
            &properties.ck_password,
            partition,
            Some(current_time),
        )
            .await
            .map_err(|e| {
                error!("{} tombstone操作失败: {}", table_full_name, e);
                Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("{} tombstone操作失败: {}", table_full_name, e),
                )) as Box<dyn Error>
            })?;

        Ok(())
    }
}
