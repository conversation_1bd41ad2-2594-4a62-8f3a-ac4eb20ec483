use crate::config::StreamConfig;
use crate::error::CkProviderError;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::sync::{mpsc, Semaphore};
use tokio::time::{sleep, Instant as TokioInstant};

/// Generic trait for stream writers that can handle different output formats
pub trait StreamWriter<T>: Send {
    type Error: std::error::Error + Send + Sync + 'static;

    /// Write a batch of items
    fn write_batch(&mut self, batch: Vec<T>) -> impl std::future::Future<Output = Result<(), Self::Error>> + Send;

    /// Flush any pending data
    fn flush(&mut self) -> impl std::future::Future<Output = Result<(), Self::Error>> + Send;

    /// Close the writer and finalize output
    fn close(self) -> impl std::future::Future<Output = Result<(), Self::Error>> + Send;
}

/// Generic stream processor for consuming channel data and writing to any output format
pub struct GenericStreamProcessor<T, W> {
    receiver: mpsc::Receiver<Option<Vec<T>>>,
    writer: W,
    config: StreamConfig,
    batch_buffer: Vec<T>,
    last_flush_time: TokioInstant,
    is_running: Arc<std::sync::atomic::AtomicBool>,
    // 并行flush相关字段
    flush_semaphore: Arc<Semaphore>,
    pending_flushes: Arc<AtomicUsize>,
}

impl<T, W> GenericStreamProcessor<T, W>
where
    T: Send + Sync + 'static,
    W: StreamWriter<T> + Send + 'static,
    W::Error: Into<CkProviderError>,
{
    /// Create a new generic stream processor
    pub fn new(
        receiver: mpsc::Receiver<Option<Vec<T>>>,
        writer: W,
        config: StreamConfig,
    ) -> Self {
        let batch_capacity = config.batch_size;
        // 使用配置中的并发flush数量
        let max_concurrent_flushes = if config.enable_parallel_flush {
            config.max_concurrent_flushes
        } else {
            1 // 禁用并行flush时只允许1个并发
        };

        Self {
            receiver,
            writer,
            config,
            batch_buffer: Vec::with_capacity(batch_capacity),
            last_flush_time: TokioInstant::now(),
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
            flush_semaphore: Arc::new(Semaphore::new(max_concurrent_flushes)),
            pending_flushes: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// Start processing the stream
    pub async fn start(mut self) -> Result<(), CkProviderError> {
        self.is_running.store(true, std::sync::atomic::Ordering::Relaxed);
        log::info!("Starting GenericStreamProcessor");

        let mut flush_interval = tokio::time::interval(self.config.flush_interval);
        flush_interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);

        loop {
            select! {
                // Receive data from channel
                batch_data = self.receiver.recv() => {
                    match batch_data {
                        Some(Some(batch)) if !batch.is_empty() => {
                            self.add_batch_to_buffer(batch).await?;
                        }
                        _ => {
                            // Channel closed or empty batch, flush remaining data and exit
                            log::info!("Channel closed, flushing remaining data");
                            if self.config.enable_parallel_flush {
                                self.flush_batch_parallel().await?;
                                // 等待所有pending的flush完成
                                self.wait_for_pending_flushes().await;
                            } else {
                                self.flush_batch().await?;
                            }

                            // Close the writer
                            self.writer.close().await.map_err(|e| e.into())?;
                            break;
                        }
                    }
                }

                // Periodic flush based on time interval
                _ = flush_interval.tick() => {
                    if self.should_flush_by_time() {
                        if self.config.enable_parallel_flush {
                            self.flush_batch_parallel().await?;
                        } else {
                            self.flush_batch().await?;
                        }
                    }
                }
            }
        }

        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);
        log::info!("GenericStreamProcessor stopped");
        Ok(())
    }

    /// Stop the processor gracefully
    pub fn stop(&self) {
        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);
    }

    /// Check if the processor is running
    pub fn is_running(&self) -> bool {
        self.is_running.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Add batch of items to buffer and flush if necessary
    async fn add_batch_to_buffer(&mut self, batch: Vec<T>) -> Result<(), CkProviderError> {
        if batch.is_empty() {
            return Ok(());
        }

        log::info!("接收到批量数据: {} 项", batch.len());

        // 将批量数据添加到缓冲区
        self.batch_buffer.extend(batch);

        // 检查是否需要flush
        if self.batch_buffer.len() >= self.config.batch_size {
            if self.config.enable_parallel_flush {
                self.flush_batch_parallel().await?;
            } else {
                self.flush_batch().await?;
            }
        }

        Ok(())
    }

    /// Check if batch should be flushed based on time
    fn should_flush_by_time(&self) -> bool {
        !self.batch_buffer.is_empty() && self.last_flush_time.elapsed() >= self.config.flush_interval
    }

    /// Flush the current batch with parallel processing
    async fn flush_batch_parallel(&mut self) -> Result<(), CkProviderError> {
        if self.batch_buffer.is_empty() {
            return Ok(());
        }

        let batch_size = self.batch_buffer.len();
        log::debug!("Parallel flushing batch of {} items", batch_size);

        // 移动batch数据，避免借用冲突
        let batch_data = std::mem::take(&mut self.batch_buffer);
        self.last_flush_time = TokioInstant::now();

        // 注意：对于单个writer（如Parquet文件），我们不能真正并行写入
        // 这里保持接口一致性，但实际上是串行写入
        self.write_batch_with_retry(batch_data).await?;

        Ok(())
    }

    /// 等待所有pending的flush完成
    async fn wait_for_pending_flushes(&self) {
        while self.pending_flushes.load(Ordering::Relaxed) > 0 {
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        log::info!("All pending flushes completed");
    }

    /// 带重试的批量写入
    async fn write_batch_with_retry(&mut self, batch_data: Vec<T>) -> Result<(), CkProviderError> {
        let mut retry_count = 0;
        let mut last_error = None;

        loop {
            // 对于第一次尝试，直接使用batch_data；对于重试，我们需要重新构建数据
            let result = if retry_count == 0 {
                self.writer.write_batch(batch_data).await
            } else {
                // 对于重试，我们无法重新构建数据，所以直接返回错误
                // 这是一个设计权衡：要么要求T实现Clone，要么限制重试功能
                return Err(last_error.unwrap_or_else(|| {
                    CkProviderError::BatchError("Cannot retry without Clone trait".to_string())
                }));
            };

            match result {
                Ok(()) => {
                    if retry_count > 0 {
                        log::info!("Batch write succeeded after {} retries", retry_count);
                    }
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e.into());
                    retry_count += 1;

                    if retry_count <= self.config.max_retries {
                        let delay = Self::calculate_retry_delay(retry_count);
                        log::warn!(
                            "Batch write failed (attempt {}/{}), retrying in {:?}: {}",
                            retry_count,
                            self.config.max_retries + 1,
                            delay,
                            last_error.as_ref().unwrap()
                        );
                        sleep(delay).await;
                        // 注意：这里我们无法重试，因为batch_data已经被移动了
                        break;
                    } else {
                        break;
                    }
                }
            }
        }

        // All retries exhausted or cannot retry
        let error = last_error.unwrap_or_else(|| {
            CkProviderError::BatchError("Unknown error during batch processing".to_string())
        });

        log::error!("Failed to write batch after {} attempts: {}", retry_count, error);
        Err(error)
    }

    /// Flush the current batch (serial version)
    async fn flush_batch(&mut self) -> Result<(), CkProviderError> {
        if self.batch_buffer.is_empty() {
            return Ok(());
        }

        let batch_size = self.batch_buffer.len();
        log::debug!("Flushing batch of {} items", batch_size);

        // 移动batch数据
        let batch_data = std::mem::take(&mut self.batch_buffer);
        self.last_flush_time = TokioInstant::now();

        self.write_batch_with_retry(batch_data).await?;

        Ok(())
    }

    /// Calculate retry delay with exponential backoff
    fn calculate_retry_delay(retry_count: u32) -> Duration {
        let base_delay = Duration::from_millis(100);
        let max_delay = Duration::from_secs(5);

        let delay = base_delay * (2_u32.pow(retry_count.saturating_sub(1)));
        std::cmp::min(delay, max_delay)
    }
}

/// Builder for creating GenericStreamProcessor instances
pub struct GenericStreamProcessorBuilder<T, W> {
    receiver: Option<mpsc::Receiver<Option<Vec<T>>>>,
    writer: Option<W>,
    config: Option<StreamConfig>,
}

impl<T, W> Default for GenericStreamProcessorBuilder<T, W>
where
    T: Send + Sync + 'static,
    W: StreamWriter<T> + Send + 'static,
    W::Error: Into<CkProviderError>,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<T, W> GenericStreamProcessorBuilder<T, W>
where
    T: Send + Sync + 'static,
    W: StreamWriter<T> + Send + 'static,
    W::Error: Into<CkProviderError>,
{
    /// Create a new builder
    pub fn new() -> Self {
        Self { receiver: None, writer: None, config: None }
    }

    /// Set the receiver
    pub fn with_receiver(mut self, receiver: mpsc::Receiver<Option<Vec<T>>>) -> Self {
        self.receiver = Some(receiver);
        self
    }

    /// Set the writer
    pub fn with_writer(mut self, writer: W) -> Self {
        self.writer = Some(writer);
        self
    }

    /// Set the configuration
    pub fn with_config(mut self, config: StreamConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// Build the processor
    pub fn build(self) -> Result<GenericStreamProcessor<T, W>, CkProviderError> {
        let receiver = self
            .receiver
            .ok_or_else(|| CkProviderError::StreamConfigError("Receiver is required".to_string()))?;

        let writer = self
            .writer
            .ok_or_else(|| CkProviderError::StreamConfigError("Writer is required".to_string()))?;

        let config = self.config.unwrap_or_default();

        Ok(GenericStreamProcessor::new(receiver, writer, config))
    }
}
