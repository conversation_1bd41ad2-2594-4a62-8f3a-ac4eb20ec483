//! ClickHouse provider crate for async streaming operations
//!
//! This crate provides a comprehensive async streaming interface for ClickHouse
//! with backpressure control, metrics monitoring, and connection management.

// Module declarations
pub mod async_sender;
pub mod config;
pub mod error;
pub mod generic_stream_processor;
pub mod provider;
pub mod stream_processor;

// Re-export public API
pub use async_sender::{AsyncCkChannel, AsyncCkSender};
pub use config::{CkConfig, StreamConfig};
pub use error::CkProviderError;
pub use generic_stream_processor::{GenericStreamProcessor, GenericStreamProcessorBuilder, StreamWriter};
pub use provider::{write_to_ck_parallel, CkProvider, CkProviderExt, CkProviderImpl, CkRowIterator};
pub use stream_processor::{CkStreamProcessor, CkStreamProcessorBuilder};
