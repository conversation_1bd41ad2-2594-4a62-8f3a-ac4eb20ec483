[package]
name = "ck-provider"
version = "0.1.0"
edition = "2021"

[dependencies]
clickhouse = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
thiserror = { workspace = true }
serde = { workspace = true }
log = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[features]
run-tests = []

[dev-dependencies]
tokio = { workspace = true }

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"
