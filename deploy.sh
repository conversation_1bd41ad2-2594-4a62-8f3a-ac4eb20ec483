#!/bin/bash
next_calculater_version=$1

pwd=`pwd`
source ../../../properties/bigdata-common.properties

echo `date '+%Y-%m-%d %H:%M:%S'`'开始部署next-calculater'

# 部署next-calculater
echo $(date '+%Y-%m-%d %H:%M:%S')'开始分发next-calculater到'$gdp_server_deploy
sshpass -p $glory_deploy_password ssh -o StrictHostKeyChecking=no $gdp_server_deploy 'mkdir -p ~/deploy/onedata/dataware/native'
sshpass -p $glory_deploy_password scp -o StrictHostKeyChecking=no libnative-$next_calculater_version.so $gdp_server_deploy:~/deploy/onedata/dataware/native
echo `date '+%Y-%m-%d %H:%M:%S'`'结束分发next-calculater到'$gdp_server_deploy

echo `date '+%Y-%m-%d %H:%M:%S'`'结束部署next-calculater'