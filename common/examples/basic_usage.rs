use ck_provider::{<PERSON>k<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON><PERSON>, CkProviderImpl};

use std::time::Duration;

use clickhouse::Row;
use common::dto::ods::product_config::OdsProductConfig;
use serde::{Deserialize, Serialize};

#[derive(Row, Serialize, Deserialize, Debug, <PERSON>lone)]
struct User {
    id: u32,
    name: String,
    age: u8,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 配置ClickHouse连接 - 使用默认配置并自定义需要的字段
    let config = CkConfig {
        url: "http://mpp01.qa.guwave.com:8123".to_string(),
        username: "admin".to_string(),
        password: "admin@ck@Guwave".to_string(),
        database: "ods".to_string(),
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
        ..Default::default() // 使用默认值填充其他字段
    };

    let ck_provider = CkProviderImpl::new(config.clone());

    let product_sql = "SELECT DISTINCT
                ifNull(DATA_SOURCE, '')
               ,ifNull(CUSTOMER, '')
               ,ifNull(SUB_CUSTOMER, '')
               ,ifNull(FACTORY, '')
               ,ifNull(FACTORY_SITE, '')
               ,ifNull(TEST_AREA, '')
               ,ifNull(TEST_STAGE, '')
               ,ifNull(DEVICE_ID, '')
               ,ifNull(PRODUCT, '')
               ,ifNull(PRODUCT_TYPE, '')
               ,ifNull(PRODUCT_FAMILY, '')
        FROM ods.ods_yms_wafermap_config_snapshot_cluster
        WHERE DATA_SOURCE = 'Test Raw Data'
          AND CUSTOMER = 'GUWAVE'
          AND FACTORY = 'CZ_343_002'
          AND TEST_AREA = 'CP'
          AND TEST_STAGE = 'CP1'
          AND DEVICE_ID = 'GUBO_PART_TYP_001'
          AND DT = (SELECT LATEST_PARTITION_VALUE
                    FROM meta.meta_table_latest_partition_cluster
                    WHERE TABLE_NAME = 'ods_yms_wafermap_config_snapshot_cluster'
                    AND DATABASE_NAME = 'ods')";

    let res = ck_provider.query::<OdsProductConfig>(&product_sql).await?;

    for item in res {
        println!("{:?}", item);
    }

    Ok(())
}
