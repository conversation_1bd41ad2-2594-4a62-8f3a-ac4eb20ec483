use mysql_provider::{MySq<PERSON><PERSON>rov<PERSON>, MySqlProviderError, MySqlProviderImpl};
use sqlx::FromRow;
use std::collections::HashMap;
use crate::dws::dws_service::DwsService;

/// Lot data count result structure
/// Corresponds to: LotData case class in Scala
#[derive(Debug, FromRow)]
pub struct LotData {
    #[sqlx(rename = "dieDataCount")]
    pub die_data_count: Option<i64>,
    #[sqlx(rename = "testItemDataCount")]
    pub test_item_data_count: Option<i64>,
}

/// File bin definition result structure
/// Corresponds to: FileBinDefinition case class in Scala
#[derive(Debug, FromRow)]
pub struct FileBinDefinition {
    #[sqlx(rename = "fileId")]
    pub file_id: i64,
    #[sqlx(rename = "useBinDefinitionFlag")]
    pub use_bin_definition_flag: i32,
}

/// Lot Stocking Detail Repository
/// Corresponds to: LotStockingDetailRepository.scala
pub struct LotStockingDetailRepository<'a> {
    mysql_provider: &'a MySqlProviderImpl,
}

impl<'a> LotStockingDetailRepository<'a> {
    /// Create new repository instance
    /// Corresponds to: LotStockingDetailRepository constructor in Scala
    pub async fn new(mysql_provider: &'a MySqlProviderImpl) -> Result<Self, MySqlProviderError> {
        Ok(Self { mysql_provider })
    }

    /// Count lot data from dw_lot_stocking_detail table
    /// Corresponds to: countLotData method in Scala
    pub async fn count_lot_data(
        &self,
        customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
    ) -> Result<LotData, MySqlProviderError> {
        let wafer_condition = if DwsService::is_cp_test_area(test_area) {
            format!("AND wafer_no = '{}'", wafer_no)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            SELECT
            CAST(COALESCE(SUM(die_data_count), 0) AS SIGNED) AS dieDataCount,
            CAST(COALESCE(SUM(test_item_data_count), 0) AS SIGNED) AS testItemDataCount
            FROM dw_lot_stocking_detail
            WHERE
            customer = '{}'
            AND factory = '{}'
            AND factory_site = '{}'
            AND test_area = '{}'
            AND device_id = '{}'
            AND lot_id = '{}'
            AND test_stage = '{}'
            AND lot_type = '{}'
            AND file_category = '{}'
            {}
            "#,
            customer,
            factory,
            factory_site,
            test_area,
            device_id,
            lot_id,
            test_stage,
            lot_type,
            file_category,
            wafer_condition
        );

        log::info!("查询条件：{}", sql);
        
        let results = self.mysql_provider.query::<LotData>(&sql).await?;
        Ok(results.into_iter().next().unwrap())
    }

    /// Query bin definition flag from dw_lot_stocking_detail and dw_lot_meta_data_detail tables
    /// Corresponds to: queryBinDefinitionFlag method in Scala
    pub async fn query_bin_definition_flag(
        &self,
        customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
    ) -> Result<HashMap<i64, i32>, MySqlProviderError> {
        let wafer_condition = if DwsService::is_cp_test_area(test_area) {
            format!("and wafer_no = '{}'", wafer_no)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            select b.file_info_id as fileId, a.use_bin_definition_flag as useBinDefinitionFlag from (
            select
            file_name, use_bin_definition_flag
            from dw_lot_stocking_detail
             where
             customer = '{}'
             and factory = '{}'
             and factory_site = '{}'
             and test_area = '{}'
             and device_id = '{}'
             and lot_id = '{}'
             and test_stage = '{}'
             and lot_type = '{}'
             and file_category = '{}'
             {}
             ) a join (
            select
            file_name, file_info_id
            from dw_lot_meta_data_detail
             where
             customer = '{}'
             and factory = '{}'
             and factory_site = '{}'
             and test_area = '{}'
             and device_id = '{}'
             and lot_id = '{}'
             and test_stage = '{}'
             and lot_type = '{}'
             and file_category = '{}'
             {}
             ) b on a.file_name = b.file_name
            "#,
            customer,
            factory,
            factory_site,
            test_area,
            device_id,
            lot_id,
            test_stage,
            lot_type,
            file_category,
            wafer_condition,
            customer,
            factory,
            factory_site,
            test_area,
            device_id,
            lot_id,
            test_stage,
            lot_type,
            file_category,
            wafer_condition
        );

        log::info!("查询条件：{}", sql);
        
        let results = self.mysql_provider.query::<FileBinDefinition>(&sql).await?;
        let map: HashMap<i64, i32> = results
            .into_iter()
            .map(|item| (item.file_id, item.use_bin_definition_flag))
            .collect();
        
        Ok(map)
    }
}