use chrono::Utc;
use mysql_provider::{MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use sqlx::FromRow;
use std::collections::HashMap;
use crate::model::key::Lot<PERSON>ey;
use crate::dws::model::mysql::dw_test_program_test_plan::DwTestProgramTestPlan;
use crate::utils::date::Format;

/// Test program manual count result structure
/// Corresponds to: TestProgramManualCnt case class in Scala
#[derive(Debug, FromRow)]
pub struct TestProgramManualCnt {
    pub test_program: String,
    pub cnt: i64,
}

/// Test Program Test Plan Repository
/// Corresponds to: TestProgramTestPlanRepository.scala
pub struct TestProgramTestPlanRepository<'a> {
    mysql_provider: &'a MySqlProviderImpl,
}

impl<'a> TestProgramTestPlanRepository<'a> {
    /// Create new repository instance
    /// Corresponds to: TestProgramTestPlanRepository constructor in Scala
    pub async fn new(mysql_provider: &'a MySqlProviderImpl) -> Result<Self, MySqlProviderError> {
        Ok(Self { mysql_provider })
    }

    /// Check if test programs can initialize test program test plan test order
    /// Corresponds to: canInitTestProgramTestPlanTestOrder method in Scala
    pub async fn can_init_test_program_test_plan_test_order(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_programs: &[String],
    ) -> Result<Vec<String>, MySqlProviderError> {
        if test_programs.is_empty() {
            return Ok(vec![]);
        }

        let test_programs_str = test_programs
            .iter()
            .map(|p| format!("'{}'", p))
            .collect::<Vec<_>>()
            .join(",");

        let sql = format!(
            r#"
            SELECT CAST(test_program as CHAR) as test_program, COUNT(1) as cnt
            FROM dw_test_program_test_plan
            WHERE customer = '{}'
              AND sub_customer = '{}'
              AND upload_type = '{}'
              AND test_area = '{}'
              AND factory = '{}'
              AND factory_site = '{}'
              AND device_id = '{}'
              AND test_stage = '{}'
              AND lot_type = '{}'
              AND test_program IN ({})
              AND (test_order_manual_import_flag = 1 OR test_order IS NOT NULL)
            GROUP BY test_program
            "#,
            lot_key.customer,
            lot_key.sub_customer,
            upload_type,
            lot_key.test_area,
            lot_key.factory,
            lot_key.factory_site,
            lot_key.device_id,
            lot_key.test_stage,
            lot_key.lot_type,
            test_programs_str
        );

        let results = self.mysql_provider.query::<TestProgramManualCnt>(&sql).await?;
        let can_map: HashMap<String, i64> = results
            .into_iter()
            .map(|t| (t.test_program, t.cnt))
            .collect();

        let filtered_programs = test_programs
            .iter()
            .filter(|t| can_map.get(*t).unwrap_or(&0) == &0)
            .cloned()
            .collect();

        Ok(filtered_programs)
    }

    /// Check if test programs can update test program test plan bin relation
    /// Corresponds to: canUpdateTestProgramTestPlanBinRelation method in Scala
    pub async fn can_update_test_program_test_plan_bin_relation(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_programs: &[String],
    ) -> Result<Vec<String>, MySqlProviderError> {
        if test_programs.is_empty() {
            return Ok(vec![]);
        }

        let test_programs_str = test_programs
            .iter()
            .map(|p| format!("'{}'", p))
            .collect::<Vec<_>>()
            .join(",");

        let sql = format!(
            r#"
            SELECT CAST(test_program as CHAR) as test_program, COUNT(1) as cnt
            FROM dw_test_program_test_plan
            WHERE customer = '{}'
              AND sub_customer = '{}'
              AND upload_type = '{}'
              AND test_area = '{}'
              AND factory = '{}'
              AND factory_site = '{}'
              AND device_id = '{}'
              AND test_stage = '{}'
              AND lot_type = '{}'
              AND test_program IN ({})
              AND bin_relation_manual_import_flag = 1
            GROUP BY test_program
            "#,
            lot_key.customer,
            lot_key.sub_customer,
            upload_type,
            lot_key.test_area,
            lot_key.factory,
            lot_key.factory_site,
            lot_key.device_id,
            lot_key.test_stage,
            lot_key.lot_type,
            test_programs_str
        );

        let results = self.mysql_provider.query::<TestProgramManualCnt>(&sql).await?;
        let can_map: HashMap<String, i64> = results
            .into_iter()
            .map(|t| (t.test_program, t.cnt))
            .collect();

        let filtered_programs = test_programs
            .iter()
            .filter(|t| can_map.get(*t).unwrap_or(&0) == &0)
            .cloned()
            .collect();

        Ok(filtered_programs)
    }

    /// Query program plan by lot key, upload type, and test program
    /// Corresponds to: queryProgramPlan method in Scala
    pub async fn query_program_plan(
        &self,
        lot_key: &LotKey,
        upload_type: &str,
        test_program: &str,
    ) -> Result<HashMap<String, DwTestProgramTestPlan>, MySqlProviderError> {
        let sql = format!(
            r#"
            SELECT id,
                   CAST(customer AS CHAR) as customer,
                   CAST(sub_customer AS CHAR) as sub_customer,
                   CAST(upload_type AS CHAR) as upload_type,
                   CAST(test_area AS CHAR) as test_area,
                   CAST(factory AS CHAR) as factory,
                   CAST(factory_site AS CHAR) as factory_site,
                   CAST(fab AS CHAR) as fab,
                   CAST(fab_site AS CHAR) as fab_site,
                   CAST(device_id AS CHAR) as device_id,
                   CAST(test_stage AS CHAR) as test_stage,
                   CAST(lot_type AS CHAR) as lot_type,
                   CAST(test_program AS CHAR) as test_program,
                   CAST(test_item AS CHAR) as test_item,
                   test_order,
                   CAST(testitem_type AS CHAR) as testitem_type,
                   test_num,
                   CAST(test_txt AS CHAR) as test_txt,
                   CAST(bin_relation AS CHAR) as bin_relation,
                   CAST(hbins AS CHAR) as hbins,
                   CAST(sbins AS CHAR) as sbins,
                   unit_scale,
                   CAST(custom_unit AS CHAR) as custom_unit,
                   test_order_manual_import_flag,
                   bin_relation_manual_import_flag,
                   create_time,
                   update_time,
                   CAST(create_user AS CHAR) as create_user,
                   CAST(update_user AS CHAR) as update_user
            FROM dw_test_program_test_plan
            WHERE customer = '{}'
              AND sub_customer = '{}'
              AND upload_type = '{}'
              AND test_area = '{}'
              AND factory = '{}'
              AND factory_site = '{}'
              AND device_id = '{}'
              AND test_stage = '{}'
              AND lot_type = '{}'
              AND test_program = '{}'
            "#,
            lot_key.customer,
            lot_key.sub_customer,
            upload_type,
            lot_key.test_area,
            lot_key.factory,
            lot_key.factory_site,
            lot_key.device_id,
            lot_key.test_stage,
            lot_key.lot_type,
            test_program
        );

        let results = self.mysql_provider.query::<DwTestProgramTestPlan>(&sql).await?;
        let plan_map = results
            .into_iter()
            .filter_map(|plan| {
                plan.test_item.clone()
                    .map(|test_item| (test_item, plan))
            })
            .collect();

        Ok(plan_map)
    }

    /// Save or update test program test plan data
    /// Corresponds to: saveOrUpdateTestProgramTestPlan method in Scala
    pub async fn save_or_update_test_program_test_plan(
        &self,
        test_program_test_plans: Vec<DwTestProgramTestPlan>,
    ) -> Result<(), MySqlProviderError> {
        if test_program_test_plans.is_empty() {
            return Ok(());
        }

        let (update_plans, insert_plans): (Vec<_>, Vec<_>) = test_program_test_plans
            .into_iter()
            .partition(|plan| plan.id.is_some());

        let mut queries = Vec::new();

        // Handle updates
        if !update_plans.is_empty() {
            log::info!("testProgramTestPlan 更新{}条", update_plans.len());
            for plan in update_plans {
                let sql = format!(
                    r#"
                    UPDATE dw_test_program_test_plan
                    SET fab = '{}',
                        fab_site = '{}',
                        test_order = {},
                        bin_relation = '{}',
                        hbins = '{}',
                        sbins = '{}',
                        update_user = '{}',
                        update_time = '{}'
                    WHERE id = {}
                    "#,
                    plan.fab.as_deref().unwrap_or(""),
                    plan.fab_site.as_deref().unwrap_or(""),
                    plan.test_order.map_or("NULL".to_string(), |v| v.to_string()),
                    plan.bin_relation.as_deref().unwrap_or(""),
                    plan.hbins.as_deref().unwrap_or(""),
                    plan.sbins.as_deref().unwrap_or(""),
                    plan.update_user.as_deref().unwrap_or(""),
                    plan.update_time.map_or(Utc::now().into_format(), |t| t.into_format()),
                    plan.id.unwrap()
                );
                queries.push(sql);
            }
        }

        // Handle inserts
        if !insert_plans.is_empty() {
            log::info!("testProgramTestPlan 新增{}条", insert_plans.len());
            for plan in insert_plans {
                let sql = format!(
                    r#"
                    INSERT INTO dw_test_program_test_plan(
                        customer, sub_customer, upload_type, test_area, factory, factory_site, fab,
                        fab_site, device_id, test_stage, lot_type, test_program, test_item, test_order,
                        testitem_type, test_num, test_txt, bin_relation, hbins, sbins, unit_scale,
                        custom_unit, test_order_manual_import_flag, bin_relation_manual_import_flag,
                        create_time, update_time, create_user, update_user
                    ) VALUES (
                        '{}', '{}', '{}', '{}', '{}', '{}', '{}',
                        '{}', '{}', '{}', '{}', '{}', '{}', {},
                        '{}', {}, '{}', '{}', '{}', '{}', {},
                        '{}', {}, {},
                        '{}', '{}', '{}', '{}'
                    )
                    "#,
                    plan.customer.as_deref().unwrap_or(""),
                    plan.sub_customer.as_deref().unwrap_or(""),
                    plan.upload_type.as_deref().unwrap_or(""),
                    plan.test_area.as_deref().unwrap_or(""),
                    plan.factory.as_deref().unwrap_or(""),
                    plan.factory_site.as_deref().unwrap_or(""),
                    plan.fab.as_deref().unwrap_or(""),
                    plan.fab_site.as_deref().unwrap_or(""),
                    plan.device_id.as_deref().unwrap_or(""),
                    plan.test_stage.as_deref().unwrap_or(""),
                    plan.lot_type.as_deref().unwrap_or(""),
                    plan.test_program.as_deref().unwrap_or(""),
                    plan.test_item.as_deref().unwrap_or(""),
                    plan.test_order.map_or("NULL".to_string(), |v| v.to_string()),
                    plan.testitem_type.as_deref().unwrap_or(""),
                    plan.test_num.map_or("NULL".to_string(), |v| v.to_string()),
                    plan.test_txt.as_deref().unwrap_or(""),
                    plan.bin_relation.as_deref().unwrap_or(""),
                    plan.hbins.as_deref().unwrap_or(""),
                    plan.sbins.as_deref().unwrap_or(""),
                    plan.unit_scale.map_or("NULL".to_string(), |v| format!("{}", v)),
                    plan.custom_unit.as_deref().unwrap_or(""),
                    plan.test_order_manual_import_flag.map_or("NULL".to_string(), |v| v.to_string()),
                    plan.bin_relation_manual_import_flag.map_or("NULL".to_string(), |v| v.to_string()),
                    plan.create_time.map_or(Utc::now().into_format(), |t| t.into_format()),
                    plan.update_time.map_or(Utc::now().into_format(), |t| t.into_format()),
                    plan.create_user.as_deref().unwrap_or(""),
                    plan.update_user.as_deref().unwrap_or("")
                );
                queries.push(sql);
            }
        }

        // Execute all queries in a transaction
        if !queries.is_empty() {
            self.mysql_provider.execute_batch(&queries).await?;
        }

        Ok(())
    }
}
