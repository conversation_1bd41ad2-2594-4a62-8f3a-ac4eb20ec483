use chrono::Utc;
use mysql_provider::{MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use crate::{model::constant::test_area::TestArea, utils::date::Format};

/// Lot Wafer Write Table Repository
/// Corresponds to: LotWaferWriteTableRepository.scala
pub struct LotWaferWriteTableRepository<'a> {
    mysql_provider: &'a MySqlProviderImpl,
}

impl<'a> LotWaferWriteTableRepository<'a> {
    /// Create new repository instance
    /// Corresponds to: LotWaferWriteTableRepository constructor in Scala
    pub async fn new(mysql_provider: &'a MySqlProviderImpl) -> Result<Self, MySqlProviderError> {
        Ok(Self { mysql_provider })
    }

    /// Count start write records for tombstone check
    /// Corresponds to: LotWaferWriteTableRepository.scala:16-49 (countStartWriteRecord method)
    pub async fn count_start_write_record(
        &self,
        customer: &str,
        factory: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
        table_alias_name: &str,
    ) -> Result<i64, MySqlProviderError> {
        let support_cp_test_area_list = TestArea::get_cp_list();
        let wafer_condition = if let Some(test_area_enum) = TestArea::of(test_area) {
            if support_cp_test_area_list.contains(&test_area_enum) {
                format!(" AND wafer_no = '{}'", wafer_no)
            } else {
                String::new()
            }
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            SELECT COUNT(1) as cnt
            FROM dw_lot_wafer_write_table_record
            WHERE customer = '{}'
              AND factory = '{}'
              AND test_area = '{}'
              AND device_id = '{}'
              AND lot_id = '{}'
              AND test_stage = '{}'
              AND lot_type = '{}'
              AND file_category = '{}'
              AND step = 0
              AND table_alias_name = '{}'{}
            "#,
            customer, factory, test_area, device_id, lot_id, test_stage, lot_type, file_category, table_alias_name, wafer_condition
        );

        log::info!("查询条件：{}", sql);
        
        let result = self.mysql_provider.count(&sql).await?;
        Ok(result.unwrap_or(0))
    }

    /// Write record to lot wafer write table
    /// Corresponds to: LotWaferWriteTableRepository.scala:51-104 (writeRecord method)
    pub async fn write_record(
        &self,
        customer: &str,
        factory: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
        sub_customer: &str,
        factory_site: &str,
        table_alias_name: &str,
        write_cnt: i64,
        step: i32,
    ) -> Result<(), MySqlProviderError> {
        let now  = Utc::now();
        let sql = format!(
            r#"
            INSERT INTO dw_lot_wafer_write_table_record
            (customer, sub_customer, factory, factory_site, test_area, lot_type,
             device_id, lot_id, wafer_no, test_stage, file_category, table_alias_name,
             write_cnt, step, create_time, update_time)
            VALUES ('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', {}, {}, '{}', '{}')
            "#,
            customer, sub_customer, factory, factory_site, test_area, lot_type,
            device_id, lot_id, wafer_no, test_stage, file_category, table_alias_name,
            write_cnt, step, now.into_format(), now.into_format()
        );

        self.mysql_provider.execute(&sql).await?;
        Ok(())
    }
}