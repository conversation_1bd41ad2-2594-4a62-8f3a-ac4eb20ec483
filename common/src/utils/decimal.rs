use fixnum::{typenum::U18, FixedPoint};
use rust_decimal::Decimal;
use std::fmt;

// Decimal(38, 18)
// P = 38 (精度)：这是数字的总位数（整数部分 + 小数部分）。根据 ClickHouse 的规则：
// 1 <= P <= 9: 使用 Int32
// 10 <= P <= 18: 使用 Int64
// 19 <= P <= 38: 使用 Int128
// 39 <= P <= 76: 使用 Int256
pub type Decimal38_18 = FixedPoint<i128, U18>;

const F64_MAX: f64 = 99000000000000000000.0f64;
const MANTISSA_MAX: i128 = 99_000_000_000_000_000_000_000_000_000_000_000_000i128;
const DECIMAL38_18_MAX: Decimal38_18 = Decimal38_18::from_bits(MANTISSA_MAX);

pub trait IntoDecimal38_18 {
    fn into_decimal38_18(self) -> Decimal38_18;
}

pub trait IntoDecimal {
    fn into_decimal(self) -> Decimal;
}

pub trait TryIntoDecimal38_18 {
    fn try_into_decimal38_18(self) -> Result<Decimal38_18, ConversionError>;
}

impl IntoDecimal38_18 for f64 {
    fn into_decimal38_18(self) -> Decimal38_18 {
        if self > F64_MAX {
            log::error!("try convert f64 to Decimal38_18 {} overflow, max value is {}", self, F64_MAX);
            DECIMAL38_18_MAX
        } else {
            let decimal_str = self.to_string();
            let decimal = decimal_str
                .parse::<Decimal>()
                .unwrap_or_else(|e| panic!("Invalid decimal string '{}': {}", decimal_str, e));
            decimal.into_decimal38_18()
        }
    }
}

impl IntoDecimal for f64 {
    fn into_decimal(self) -> Decimal {
        let value = if self > F64_MAX {
            log::error!("try convert f64 to Decimal {} overflow, max value is {}", self, F64_MAX);
            F64_MAX
        } else {
            self
        };

        let decimal_str = value.to_string();
        decimal_str
            .parse::<Decimal>()
            .map_err(|_| panic!("Invalid decimal string: {}", decimal_str))
            .unwrap()
    }
}

impl IntoDecimal38_18 for f32 {
    fn into_decimal38_18(self) -> Decimal38_18 {
        let decimal_str = self.to_string();
        let decimal = decimal_str
            .parse::<Decimal>()
            .unwrap_or_else(|e| panic!("Invalid decimal string '{}': {}", decimal_str, e));
        decimal.into_decimal38_18()
    }
}

// 定义一个简单的错误类型来传递错误信息
#[derive(Debug)]
pub struct ConversionError(String);

impl fmt::Display for ConversionError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Decimal conversion failed: {}", self.0)
    }
}

impl TryIntoDecimal38_18 for Decimal {
    fn try_into_decimal38_18(self) -> Result<Decimal38_18, ConversionError> {
        let decimal_scale = self.scale();
        let target_scale = 18u32;

        let scaled_mantissa = if decimal_scale <= target_scale {
            // 需要放大 mantissa
            let power = target_scale - decimal_scale;
            // 使用 checked_pow 检查底数和指数的乘方是否溢出
            let scaling_factor = 10i128
                .checked_pow(power)
                .ok_or_else(|| ConversionError(format!("scaling factor 10^{} overflows", power)))?;

            // 使用 checked_mul 检查乘法是否溢出
            self.mantissa()
                .checked_mul(scaling_factor)
                .ok_or_else(|| ConversionError("mantissa scaling multiplication overflows".to_string()))?
        } else {
            // 除法通常较为安全，但如果除数是0也会panic，10的n次方不会是0
            // 不过为了保持代码风格一致，也可以使用 checked_div
            let power = decimal_scale - target_scale;
            let scaling_factor = 10i128
                .checked_pow(power)
                .ok_or_else(|| ConversionError(format!("scaling factor 10^{} overflows", power)))?;

            self.mantissa().checked_div(scaling_factor).unwrap_or(0) // 如果除数为0（这里不可能），或者结果溢出（i128::MIN / -1），则返回0
        };
        Ok(Decimal38_18::from_bits(scaled_mantissa))
    }
}

impl IntoDecimal38_18 for Decimal {
    fn into_decimal38_18(self) -> Decimal38_18 {
        let decimal_scale = self.scale();
        let target_scale = 18u32;

        let scaled_mantissa = if decimal_scale <= target_scale {
            // 需要放大mantissa
            let scale_factor = 10i128.pow(target_scale - decimal_scale);
            self.mantissa().checked_mul(scale_factor).unwrap_or_else(|| {
                log::error!(
                    "Overflow when scaling decimal mantissa {} by factor {} (scale {} -> {}). \
                     Original decimal: {}, mantissa: {}, scale: {}",
                    self.mantissa(),
                    scale_factor,
                    decimal_scale,
                    target_scale,
                    self,
                    self.mantissa(),
                    decimal_scale
                );
                MANTISSA_MAX
            })
        } else {
            // 需要缩小mantissa
            self.mantissa() / 10i128.pow(decimal_scale - target_scale)
        };
        Decimal38_18::from_bits(scaled_mantissa)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_into_decimal38_18() {
        let decimal = "333.12345678901234567890".parse::<Decimal>().unwrap();
        let decimal38_18 = decimal.into_decimal38_18();
        assert_eq!(decimal38_18.to_string(), "333.123456789012345678");

        let f64 = 200000000000000000000000000000000f64;
        let decimal38_18 = f64.into_decimal38_18();
        assert_eq!(decimal38_18.to_string(), "99000000000000000000.0");
    }

    #[test]
    fn test_into_decimal38_18_f64() {
        let f64 = 333.12345678901234567890f32;
        let decimal38_18 = f64.into_decimal38_18();
        assert_eq!(decimal38_18.to_string(), "333.12344");
    }

    #[test]
    fn test_decimal_max() {
        assert_eq!(DECIMAL38_18_MAX.to_string(), "99000000000000000000.0")
    }
}
