use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::utils::{date::IntoDateTimeUtc, decimal::Decimal38_18};
use chrono::{DateTime, Utc};

/// FileDetail represents file metadata information used in test item detail processing
/// Corresponds to: FileDetail model in Scala implementation
/// This structure contains all file-level metadata that gets merged with SubTestItemDetail

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct SubFileDetail {
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: Arc<str>,
    pub PROCESS: Arc<str>,
    pub TEST_TEMPERATURE: Arc<str>,
    pub TESTER_NAME: Arc<str>,
    pub TESTER_TYPE: Arc<str>,
    pub PROBER_HANDLER_ID: Arc<str>,
    pub PROBECARD_LOADBOARD_ID: Arc<str>,
    pub PROBER_HANDLER_TYP: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP: Arc<str>,
    pub RETEST_BIN_NUM: Arc<str>,
    pub UPLOAD_TIME: i64,
    pub START_TIME: Option<i64>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    pub END_TIME: Option<i64>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,
}

impl SubFileDetail {
    pub fn from_file_detail(file_detail: &FileDetail) -> Self {
        Self {
            UPLOAD_TYPE: Arc::from(file_detail.UPLOAD_TYPE.as_str()),
            CUSTOMER: Arc::from(file_detail.CUSTOMER.as_str()),
            SUB_CUSTOMER: Arc::from(file_detail.SUB_CUSTOMER.as_str()),
            FAB: Arc::from(file_detail.FAB.as_str()),
            FAB_SITE: Arc::from(file_detail.FAB_SITE.as_str()),
            FACTORY: Arc::from(file_detail.FACTORY.as_str()),
            FACTORY_SITE: Arc::from(file_detail.FACTORY_SITE.as_str()),
            TEST_AREA: Arc::from(file_detail.TEST_AREA.as_str()),
            TEST_STAGE: Arc::from(file_detail.TEST_STAGE.as_str()),
            DEVICE_ID: Arc::from(file_detail.DEVICE_ID.as_str()),
            LOT_TYPE: Arc::from(file_detail.LOT_TYPE.as_str()),
            LOT_ID: Arc::from(file_detail.LOT_ID.as_str()),
            SBLOT_ID: Arc::from(file_detail.SBLOT_ID.as_str()),
            TEST_PROGRAM: Arc::from(file_detail.TEST_PROGRAM.as_str()),
            TEST_PROGRAM_VERSION: Arc::from(file_detail.TEST_PROGRAM_VERSION.as_str()),
            SITE_CNT: file_detail.SITE_CNT,
            SITE_NUMS: Arc::from(file_detail.SITE_NUMS.as_str()),
            PROCESS: Arc::from(file_detail.PROCESS.as_deref().unwrap_or("")),
            TEST_TEMPERATURE: Arc::from(file_detail.TEST_TEMPERATURE.as_str()),
            TESTER_NAME: Arc::from(file_detail.TESTER_NAME.as_str()),
            TESTER_TYPE: Arc::from(file_detail.TESTER_TYPE.as_str()),
            PROBER_HANDLER_ID: Arc::from(file_detail.PROBER_HANDLER_ID.as_str()),
            PROBECARD_LOADBOARD_ID: Arc::from(file_detail.PROBECARD_LOADBOARD_ID.as_str()),
            PROBER_HANDLER_TYP: Arc::from(file_detail.PROBER_HANDLER_TYP.as_str()),
            PROBECARD_LOADBOARD_TYP: Arc::from(file_detail.PROBECARD_LOADBOARD_TYP.as_str()),
            RETEST_BIN_NUM: Arc::from(file_detail.RETEST_BIN_NUM.as_str()),
            UPLOAD_TIME: file_detail.UPLOAD_TIME.map(|dt| dt.timestamp_millis()).unwrap_or(0),
            START_TIME: file_detail.START_TIME.map(|dt| dt.timestamp_millis()),
            START_HOUR_KEY: Arc::from(file_detail.START_HOUR_KEY.as_str()),
            START_DAY_KEY: Arc::from(file_detail.START_DAY_KEY.as_str()),
            END_TIME: file_detail.END_TIME.map(|dt| dt.timestamp_millis()),
            END_HOUR_KEY: Arc::from(file_detail.END_HOUR_KEY.as_str()),
            END_DAY_KEY: Arc::from(file_detail.END_DAY_KEY.as_str()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct FileDetail {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub DEVICE_ID: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub TEST_AREA: String,
    pub TEST_STAGE: String,
    pub OFFLINE_RETEST: Option<u8>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub INTERRUPT_IGNORE_TP: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub DUP_RETEST_IGNORE_TP: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub BATCH_NUM_IGNORE_TP: Option<u8>,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub SPEC_NAM: String,
    pub SPEC_VER: String,
    pub TESTER_NAME: String,
    pub TESTER_TYPE: String,
    pub OPERATOR_NAME: String,
    pub PROBER_HANDLER_TYP: String,
    pub PROBER_HANDLER_ID: String,
    pub PROBECARD_LOADBOARD_TYP: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub SITE_GRP: Option<u32>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: String,
    pub START_TIME: Option<DateTime<Utc>>,
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub WAFER_SIZE: Option<Decimal38_18>,
    pub WAFER_MARGIN: Option<Decimal38_18>,
    pub DIE_HEIGHT: Option<Decimal38_18>,
    pub DIE_WIDTH: Option<Decimal38_18>,
    pub WF_UNITS: Option<u32>,
    pub WF_FLAT: String,
    pub CENTER_X: Option<i32>,
    pub CENTER_Y: Option<i32>,
    pub CENTER_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_X: Option<i32>,
    pub CENTER_RETICLE_Y: Option<i32>,
    pub CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub POS_X: String,
    pub POS_Y: String,
    pub DIE_CNT: Option<u32>,
    pub RETICLE_ROW: Option<u32>,
    pub RETICLE_COLUMN: Option<u32>,
    pub RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_WAFER_SIZE: Option<Decimal38_18>,
    pub ORIGINAL_WAFER_MARGIN: Option<Decimal38_18>,
    pub ORIGINAL_WF_UNITS: Option<u32>,
    pub ORIGINAL_WF_FLAT: String,
    pub ORIGINAL_POS_X: String,
    pub ORIGINAL_POS_Y: String,
    pub ORIGINAL_DIE_WIDTH: Option<Decimal38_18>,
    pub ORIGINAL_DIE_HEIGHT: Option<Decimal38_18>,
    pub ORIGINAL_RETICLE_ROW: Option<u32>,
    pub ORIGINAL_RETICLE_COLUMN: Option<u32>,
    pub ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_CENTER_X: Option<i32>,
    pub ORIGINAL_CENTER_Y: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_X: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_Y: Option<i32>,
    pub ORIGINAL_CENTER_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub PART_CNT: Option<u32>,
    pub RTST_CNT: Option<u32>,
    pub ABRT_CNT: Option<u32>,
    pub GOOD_CNT: Option<u32>,
    pub FUNC_CNT: Option<u32>,
    pub FABWF_ID: String,
    pub FRAME_ID: String,
    pub MASK_ID: String,
    pub WAFER_USR_DESC: String,
    pub WAFER_EXC_DESC: String,
    pub SETUP_T: Option<DateTime<Utc>>,
    pub STAT_NUM: Option<u32>,
    pub MODE_COD: String,
    pub PROT_COD: String,
    pub BURN_TIM: Option<u32>,
    pub CMOD_COD: String,
    pub EXEC_TYP: String,
    pub EXEC_VER: String,
    pub USER_TXT: String,
    pub AUX_FILE: String,
    pub PKG_TYP: String,
    pub FAMLY_ID: String,
    pub DATE_COD: String,
    pub FACIL_ID: String,
    pub FLOOR_ID: String,
    pub PROC_ID: String,
    pub OPER_FRQ: String,
    pub FLOW_ID: String,
    pub FLOW_ID_IGNORE_TP: String,
    pub SETUP_ID: String,
    pub DSGN_REV: String,
    pub ENG_ID: String,
    pub ROM_COD: String,
    pub SERL_NUM: String,
    pub SUPR_NAM: String,
    pub DISP_COD: String,
    pub LOT_USR_DESC: String,
    pub LOT_EXC_DESC: String,
    pub DIB_TYP: String,
    pub DIB_ID: String,
    pub CABL_TYP: String,
    pub CABL_ID: String,
    pub CONT_TYP: String,
    pub CONT_ID: String,
    pub LASR_TYP: String,
    pub LASR_ID: String,
    pub EXTR_TYP: String,
    pub EXTR_ID: String,
    pub RETEST_BIN_NUM: String,
    pub CREATE_USER: String,
    pub LOT_BUCKET: i32,
    pub IS_DELETE: u8,
    pub PROCESS: Option<String>,
    pub UPLOAD_TIME: Option<DateTime<Utc>>,
    pub DATA_VERSION: Option<i64>,
}

/// FileDetailService provides functionality for creating and managing FileDetail broadcast maps
/// Corresponds to: FileDetailService.scala in the Scala implementation
pub struct FileDetailService;

impl FileDetailService {
    /// Create a new FileDetailService instance
    pub fn new() -> Self {
        Self
    }

    /// Build FileDetail from DieDetail and create broadcast map
    ///
    /// Corresponds to: FileDetailService.scala:398
    /// def broadcastFileDetail(spark: SparkSession, dieDetail: Dataset[DieDetail]): Broadcast[Map[java.lang.Long, FileDetail]]
    pub fn broadcast_file_detail(
        &self,
        die_details: &[crate::dto::dwd::die_detail_parquet::DieDetailParquet],
    ) -> Result<std::collections::HashMap<i64, FileDetail>, Box<dyn std::error::Error>> {
        self.build_file_detail(die_details)
    }

    /// Build FileDetail from DieDetail data
    ///
    /// Corresponds to: FileDetailService.scala:58-196 (buildFileDetail method)
    /// Groups DieDetail by FILE_ID and takes first record from each group to create FileDetail
    fn build_file_detail(
        &self,
        die_details: &[crate::dto::dwd::die_detail_parquet::DieDetailParquet],
    ) -> Result<std::collections::HashMap<i64, FileDetail>, Box<dyn std::error::Error>> {
        let mut file_detail_map = std::collections::HashMap::new();

        // Group die details by FILE_ID (corresponds to dieDetail.groupByKey(_.FILE_ID) in Scala)
        let mut grouped_by_file_id: std::collections::HashMap<
            i64,
            Vec<&crate::dto::dwd::die_detail_parquet::DieDetailParquet>,
        > = std::collections::HashMap::new();

        for die_detail in die_details {
            if let Some(file_id) = die_detail.FILE_ID {
                grouped_by_file_id.entry(file_id).or_insert_with(Vec::new).push(die_detail);
            }
        }

        // For each FILE_ID group, take the first record and convert to FileDetail
        // Corresponds to: mapGroups { case (_, values) => val die = values.next() ... }
        for (file_id, die_group) in grouped_by_file_id {
            if let Some(die) = die_group.first().cloned().cloned() {
                // Convert DieDetailParquet to FileDetail (corresponds to buildFileDetail in Scala)
                let file_detail = FileDetail {
                    CUSTOMER: die.CUSTOMER.unwrap(),
                    SUB_CUSTOMER: die.SUB_CUSTOMER.unwrap(),
                    UPLOAD_TYPE: die.UPLOAD_TYPE.unwrap(),
                    FILE_ID: die.FILE_ID.unwrap(),
                    FILE_NAME: die.FILE_NAME.unwrap(),
                    FILE_TYPE: die.FILE_TYPE.unwrap(),
                    DEVICE_ID: die.DEVICE_ID.unwrap(),
                    FACTORY: die.FACTORY.unwrap(),
                    FACTORY_SITE: die.FACTORY_SITE.unwrap(),
                    FAB: die.FAB.unwrap(),
                    FAB_SITE: die.FAB_SITE.unwrap(),
                    LOT_TYPE: die.LOT_TYPE.unwrap(),
                    LOT_ID: die.LOT_ID.unwrap(),
                    SBLOT_ID: die.SBLOT_ID.unwrap(),
                    TEST_AREA: die.TEST_AREA.unwrap(),
                    TEST_STAGE: die.TEST_STAGE.unwrap(),
                    OFFLINE_RETEST: die.OFFLINE_RETEST.map(|v| v as u8),
                    OFFLINE_RETEST_IGNORE_TP: die.OFFLINE_RETEST_IGNORE_TP.map(|v| v as u8),
                    INTERRUPT: die.INTERRUPT.map(|v| v as u8),
                    INTERRUPT_IGNORE_TP: die.INTERRUPT_IGNORE_TP.map(|v| v as u8),
                    DUP_RETEST: die.DUP_RETEST.map(|v| v as u8),
                    DUP_RETEST_IGNORE_TP: die.DUP_RETEST_IGNORE_TP.map(|v| v as u8),
                    BATCH_NUM: die.BATCH_NUM.map(|v| v as u8),
                    BATCH_NUM_IGNORE_TP: die.BATCH_NUM_IGNORE_TP.map(|v| v as u8),
                    TEST_PROGRAM: die.TEST_PROGRAM.unwrap(),
                    TEST_TEMPERATURE: die.TEST_TEMPERATURE.unwrap(),
                    TEST_PROGRAM_VERSION: die.TEST_PROGRAM_VERSION.unwrap(),
                    SPEC_NAM: die.SPEC_NAM.unwrap(),
                    SPEC_VER: die.SPEC_VER.unwrap(),
                    TESTER_NAME: die.TESTER_NAME.unwrap(),
                    TESTER_TYPE: die.TESTER_TYPE.unwrap(),
                    OPERATOR_NAME: die.OPERATOR_NAME.unwrap(),
                    PROBER_HANDLER_TYP: die.PROBER_HANDLER_TYP.unwrap(),
                    PROBER_HANDLER_ID: die.PROBER_HANDLER_ID.unwrap(),
                    PROBECARD_LOADBOARD_TYP: die.PROBECARD_LOADBOARD_TYP.unwrap(),
                    PROBECARD_LOADBOARD_ID: die.PROBECARD_LOADBOARD_ID.unwrap(),
                    SITE_GRP: die.SITE_GRP.map(|v| v as u32),
                    SITE_CNT: die.SITE_CNT.map(|v| v as u32),
                    SITE_NUMS: die.SITE_NUMS.unwrap(),
                    START_TIME: die.START_TIME.map(|v| v.into_utc()),
                    END_TIME: die.END_TIME.map(|v| v.into_utc()),
                    START_HOUR_KEY: die.START_HOUR_KEY.unwrap(),
                    START_DAY_KEY: die.START_DAY_KEY.unwrap(),
                    END_HOUR_KEY: die.END_HOUR_KEY.unwrap(),
                    END_DAY_KEY: die.END_DAY_KEY.unwrap(),
                    WAFER_SIZE: die.WAFER_SIZE,
                    WAFER_MARGIN: die.WAFER_MARGIN,
                    DIE_HEIGHT: die.DIE_HEIGHT,
                    DIE_WIDTH: die.DIE_WIDTH,
                    WF_UNITS: die.WF_UNITS.map(|v| v as u32),
                    WF_FLAT: die.WF_FLAT.unwrap(),
                    CENTER_X: die.CENTER_X,
                    CENTER_Y: die.CENTER_Y,
                    CENTER_OFFSET_X: die.CENTER_OFFSET_X,
                    CENTER_OFFSET_Y: die.CENTER_OFFSET_Y,
                    CENTER_RETICLE_X: die.CENTER_RETICLE_X,
                    CENTER_RETICLE_Y: die.CENTER_RETICLE_Y,
                    CENTER_RETICLE_OFFSET_X: die.CENTER_RETICLE_OFFSET_X,
                    CENTER_RETICLE_OFFSET_Y: die.CENTER_RETICLE_OFFSET_Y,
                    POS_X: die.POS_X.unwrap(),
                    POS_Y: die.POS_Y.unwrap(),
                    DIE_CNT: die.DIE_CNT.map(|v| v as u32),
                    RETICLE_ROW: die.RETICLE_ROW.map(|v| v as u32),
                    RETICLE_COLUMN: die.RETICLE_COLUMN.map(|v| v as u32),
                    RETICLE_ROW_CENTER_OFFSET: die.RETICLE_ROW_CENTER_OFFSET,
                    RETICLE_COLUMN_CENTER_OFFSET: die.RETICLE_COLUMN_CENTER_OFFSET,
                    ORIGINAL_WAFER_SIZE: die.ORIGINAL_WAFER_SIZE,
                    ORIGINAL_WAFER_MARGIN: die.ORIGINAL_WAFER_MARGIN,
                    ORIGINAL_WF_UNITS: die.ORIGINAL_WF_UNITS.map(|v| v as u32),
                    ORIGINAL_WF_FLAT: die.ORIGINAL_WF_FLAT.unwrap(),
                    ORIGINAL_POS_X: die.ORIGINAL_POS_X.unwrap(),
                    ORIGINAL_POS_Y: die.ORIGINAL_POS_Y.unwrap(),
                    ORIGINAL_DIE_WIDTH: die.ORIGINAL_DIE_WIDTH,
                    ORIGINAL_DIE_HEIGHT: die.ORIGINAL_DIE_HEIGHT,
                    ORIGINAL_RETICLE_ROW: die.ORIGINAL_RETICLE_ROW.map(|v| v as u32),
                    ORIGINAL_RETICLE_COLUMN: die.ORIGINAL_RETICLE_COLUMN.map(|v| v as u32),
                    ORIGINAL_RETICLE_ROW_CENTER_OFFSET: die.ORIGINAL_RETICLE_ROW_CENTER_OFFSET,
                    ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: die.ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET,
                    ORIGINAL_CENTER_X: die.ORIGINAL_CENTER_X,
                    ORIGINAL_CENTER_Y: die.ORIGINAL_CENTER_Y,
                    ORIGINAL_CENTER_RETICLE_X: die.ORIGINAL_CENTER_RETICLE_X,
                    ORIGINAL_CENTER_RETICLE_Y: die.ORIGINAL_CENTER_RETICLE_Y,
                    ORIGINAL_CENTER_OFFSET_X: die.ORIGINAL_CENTER_OFFSET_X,
                    ORIGINAL_CENTER_OFFSET_Y: die.ORIGINAL_CENTER_OFFSET_Y,
                    ORIGINAL_CENTER_RETICLE_OFFSET_X: die.ORIGINAL_CENTER_RETICLE_OFFSET_X,
                    ORIGINAL_CENTER_RETICLE_OFFSET_Y: die.ORIGINAL_CENTER_RETICLE_OFFSET_Y,
                    PART_CNT: die.PART_CNT.map(|v| v as u32),
                    RTST_CNT: die.RTST_CNT.map(|v| v as u32),
                    ABRT_CNT: die.ABRT_CNT.map(|v| v as u32),
                    GOOD_CNT: die.GOOD_CNT.map(|v| v as u32),
                    FUNC_CNT: die.FUNC_CNT.map(|v| v as u32),
                    FABWF_ID: die.FABWF_ID.unwrap(),
                    FRAME_ID: die.FRAME_ID.unwrap(),
                    MASK_ID: die.MASK_ID.unwrap(),
                    WAFER_USR_DESC: die.WAFER_USR_DESC.unwrap(),
                    WAFER_EXC_DESC: die.WAFER_EXC_DESC.unwrap(),
                    SETUP_T: die.SETUP_T.map(|v| v.into_utc()),
                    STAT_NUM: die.STAT_NUM.map(|v| v as u32),
                    MODE_COD: die.MODE_COD.unwrap(),
                    PROT_COD: die.PROT_COD.unwrap(),
                    BURN_TIM: die.BURN_TIM.map(|v| v as u32),
                    CMOD_COD: die.CMOD_COD.unwrap(),
                    EXEC_TYP: die.EXEC_TYP.unwrap(),
                    EXEC_VER: die.EXEC_VER.unwrap(),
                    USER_TXT: die.USER_TXT.unwrap(),
                    AUX_FILE: die.AUX_FILE.unwrap(),
                    PKG_TYP: die.PKG_TYP.unwrap(),
                    FAMLY_ID: die.FAMLY_ID.unwrap(),
                    DATE_COD: die.DATE_COD.unwrap(),
                    FACIL_ID: die.FACIL_ID.unwrap(),
                    FLOOR_ID: die.FLOOR_ID.unwrap(),
                    PROC_ID: die.PROC_ID.unwrap(),
                    OPER_FRQ: die.OPER_FRQ.unwrap(),
                    FLOW_ID: die.FLOW_ID.unwrap(),
                    FLOW_ID_IGNORE_TP: die.FLOW_ID_IGNORE_TP.unwrap(),
                    SETUP_ID: die.SETUP_ID.unwrap(),
                    DSGN_REV: die.DSGN_REV.unwrap(),
                    ENG_ID: die.ENG_ID.unwrap(),
                    ROM_COD: die.ROM_COD.unwrap(),
                    SERL_NUM: die.SERL_NUM.unwrap(),
                    SUPR_NAM: die.SUPR_NAM.unwrap(),
                    DISP_COD: die.DISP_COD.unwrap(),
                    LOT_USR_DESC: die.LOT_USR_DESC.unwrap(),
                    LOT_EXC_DESC: die.LOT_EXC_DESC.unwrap(),
                    DIB_TYP: die.DIB_TYP.unwrap(),
                    DIB_ID: die.DIB_ID.unwrap(),
                    CABL_TYP: die.CABL_TYP.unwrap(),
                    CABL_ID: die.CABL_ID.unwrap(),
                    CONT_TYP: die.CONT_TYP.unwrap(),
                    CONT_ID: die.CONT_ID.unwrap(),
                    LASR_TYP: die.LASR_TYP.unwrap(),
                    LASR_ID: die.LASR_ID.unwrap(),
                    EXTR_TYP: die.EXTR_TYP.unwrap(),
                    EXTR_ID: die.EXTR_ID.unwrap(),
                    RETEST_BIN_NUM: die.RETEST_BIN_NUM.unwrap(),
                    CREATE_USER: die.CREATE_USER.unwrap(),
                    LOT_BUCKET: die.LOT_BUCKET.unwrap(),
                    IS_DELETE: die.IS_DELETE.unwrap() as u8,
                    PROCESS: die.PROCESS,
                    UPLOAD_TIME: die.UPLOAD_TIME.map(|v| v.into_utc()),
                    DATA_VERSION: die.DATA_VERSION,
                };

                // Insert into map (corresponds to toFileDetailMap in Scala)
                file_detail_map.insert(file_id, file_detail);
            }
        }

        Ok(file_detail_map)
    }
}
