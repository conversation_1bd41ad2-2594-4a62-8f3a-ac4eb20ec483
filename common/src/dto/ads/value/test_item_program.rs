use crate::dto::ads::test_item_detail::TestItemDetail;
use crate::dto::ads::{group_detail, mk_string_distinct, to_decimal};
use crate::model::constant::{EMPTY, M, P, PF_PASS, SYSTEM, ZERO};
use crate::utils::date::{get_day, get_day_hour};
use crate::utils::decimal::Decimal38_18;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;

/// ADS TestItemProgram aggregation result structure
/// Contains statistical analysis aggregated at the program level
/// Corresponds to ads_yms_stage_test_item_program_cluster table
#[derive(Debug, Clone, Serialize, Deserialize, Row, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemProgram {
    // Data source and upload information
    pub DATA_SOURCE: Arc<str>,
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,

    // Factory and location information
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,

    // Product and lot information
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub WAFER_ID: Arc<str>,
    pub WAFER_NO: Arc<str>,
    pub WAFER_LOT_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,

    // Test program information
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,

    // Processing flags
    pub IS_PASS_ONLY: u8,
    pub IS_FINAL: u8,

    // List fields for aggregated data
    pub UNITS_LIST: Arc<str>,
    pub ORIGIN_UNITS_LIST: Arc<str>,
    pub LO_LIMIT_LIST: Arc<str>,
    pub HI_LIMIT_LIST: Arc<str>,
    pub ORIGIN_LO_LIMIT_LIST: Arc<str>,
    pub ORIGIN_HI_LIMIT_LIST: Arc<str>,
    pub PROCESS_LIST: Arc<str>,
    pub TESTITEM_TYPE_LIST: Arc<str>,
    pub TEST_TEMPERATURE_LIST: Arc<str>,
    pub TESTER_NAME_LIST: Arc<str>,
    pub TESTER_TYPE_LIST: Arc<str>,
    pub PROBER_HANDLER_TYP_LIST: Arc<str>,
    pub PROBER_HANDLER_ID_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_TYP_LIST: Arc<str>,
    pub PROBECARD_LOADBOARD_ID_LIST: Arc<str>,

    // Count fields
    pub INPUT_CNT: i32,
    pub PASS_CNT: i32,
    pub FAIL_CNT: i32,
    pub PASSBIN_FAILINGITEM_CNT: i32,
    pub EXE_INPUT_CNT: i32,
    pub EXE_PASS_CNT: i32,
    pub EXE_FAIL_CNT: i32,

    // Statistical metrics - basic
    pub MEDIAN: Option<Decimal38_18>,
    pub MEAN: Option<Decimal38_18>,
    pub MAX: Option<Decimal38_18>,
    pub MIN: Option<Decimal38_18>,
    pub MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub SUM_SQ: Option<Decimal38_18>,
    pub SUM_VALUE: Option<Decimal38_18>,
    pub STDEV_P: Option<Decimal38_18>,
    pub STDEV_S: Option<Decimal38_18>,
    pub RANGE: Option<Decimal38_18>,
    pub IQR: Option<Decimal38_18>,

    // Quantiles
    pub Q1: Option<Decimal38_18>,
    pub Q3: Option<Decimal38_18>,
    pub LOWER: Option<Decimal38_18>,
    pub UPPER: Option<Decimal38_18>,
    pub OUTLIER_CNT: u32,
    pub P1: Option<Decimal38_18>,
    pub P5: Option<Decimal38_18>,
    pub P10: Option<Decimal38_18>,
    pub P90: Option<Decimal38_18>,
    pub P95: Option<Decimal38_18>,
    pub P99: Option<Decimal38_18>,

    // Histogram data
    pub GROUP_DETAIL: Vec<(String, u32)>,

    // Process capability indices
    pub PP: Option<Decimal38_18>,
    pub PPU: Option<Decimal38_18>,
    pub PPL: Option<Decimal38_18>,
    pub PPK: Option<Decimal38_18>,
    pub CP: Option<Decimal38_18>,
    pub CPU: Option<Decimal38_18>,
    pub CPL: Option<Decimal38_18>,
    pub CPK: Option<Decimal38_18>,
    pub CA: Option<Decimal38_18>,

    // Advanced statistical metrics
    pub SKEWNESS: Option<Decimal38_18>,
    pub KURTOSIS: Option<Decimal38_18>,

    // Normalization metrics
    pub NORMALIZATION_MEDIAN: Option<Decimal38_18>,
    pub NORMALIZATION_MEAN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX: Option<Decimal38_18>,
    pub NORMALIZATION_MIN: Option<Decimal38_18>,
    pub NORMALIZATION_MAX_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_MIN_WO_OUTLIERS: Option<Decimal38_18>,
    pub NORMALIZATION_IQR: Option<Decimal38_18>,
    pub NORMALIZATION_Q1: Option<Decimal38_18>,
    pub NORMALIZATION_Q3: Option<Decimal38_18>,
    pub NORMALIZATION_LOWER: Option<Decimal38_18>,
    pub NORMALIZATION_UPPER: Option<Decimal38_18>,

    // Timestamps
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: Arc<str>,
    pub START_DAY_KEY: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub END_HOUR_KEY: Arc<str>,
    pub END_DAY_KEY: Arc<str>,

    // System fields
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_HOUR_KEY: Arc<str>,
    pub CREATE_DAY_KEY: Arc<str>,
    pub CREATE_USER: Arc<str>,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub VERSION: i64,
    pub IS_DELETE: u8,
}

impl TestItemProgram {
    /// Creates a new TestItemProgram from a list of TestItemDetail items
    /// This is the equivalent of the Scala adsTestItemProgram method
    pub fn from_test_items(
        is_cp: bool,
        data_source: &str,
        items: &Vec<Arc<TestItemDetail>>,
        is_pass_only: u8,
        product_list: &[crate::dto::ods::product_config::OdsProductConfig],
    ) -> Self {

        let item = &items[0]; // Get first item for common fields
        let testitem_type = item.TESTITEM_TYPE.as_ref();

        // Filter test values for statistical calculations
        let mut test_values: Vec<f64> = items
            .iter()
            .filter(|t| t.TEST_VALUE.is_some() && t.TEST_RESULT.is_some() && t.TEST_RESULT.unwrap() < 2)
            .map(|t| t.TEST_VALUE.unwrap())
            .collect();
        test_values.sort_by(|a, b| a.partial_cmp(b).unwrap());

        // Get product information
        let (product, product_type, product_family) = if !product_list.is_empty() {
            let product_info = &product_list[0];
            (
                product_info.PRODUCT.clone(),
                product_info.PRODUCT_TYPE.clone(),
                product_info.PRODUCT_FAMILY.clone(),
            )
        } else {
            (Arc::from(EMPTY), Arc::from(EMPTY), Arc::from(EMPTY))
        };

        let now = Utc::now();
        let create_hour_key = get_day_hour(now);
        let create_day_key = get_day(now);

        // Build distinct lists
        let sblot_id_arr = mk_string_distinct(items.iter().map(|i| i.SBLOT_ID.as_ref()).collect());
        let wafer_no_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_NO.as_ref()).collect());
        let wafer_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_ID.as_ref()).collect());
        let wafer_lot_id_arr = mk_string_distinct(items.iter().map(|i| i.WAFER_LOT_ID.as_ref()).collect());
        let units_arr = mk_string_distinct(items.iter().map(|i| i.UNITS.as_ref()).collect());
        let origin_units_arr = mk_string_distinct(items.iter().map(|i| i.ORIGIN_UNITS.as_ref()).collect());
        let lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let lo_limit_arr = mk_string_distinct(lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let hi_limit_arr = mk_string_distinct(hi_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_lo_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_LO_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_lo_limit_arr = mk_string_distinct(origin_lo_limit_strings.iter().map(|s| s.as_str()).collect());

        let origin_hi_limit_strings: Vec<String> = items
            .iter()
            .filter_map(|i| i.ORIGIN_HI_LIMIT.as_ref().map(|l| l.to_string()))
            .collect();
        let origin_hi_limit_arr = mk_string_distinct(origin_hi_limit_strings.iter().map(|s| s.as_str()).collect());
        let process_arr = mk_string_distinct(items.iter().map(|i| i.PROCESS.as_ref()).collect());
        let testitem_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTITEM_TYPE.as_ref()).collect());
        let test_temperature_arr = mk_string_distinct(items.iter().map(|i| i.TEST_TEMPERATURE.as_ref()).collect());
        let tester_name_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_NAME.as_ref()).collect());
        let tester_type_arr = mk_string_distinct(items.iter().map(|i| i.TESTER_TYPE.as_ref()).collect());
        let prober_handler_id_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_ID.as_ref()).collect());
        let probecard_loadboard_id_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_ID.as_ref()).collect());
        let prober_handler_typ_arr = mk_string_distinct(items.iter().map(|i| i.PROBER_HANDLER_TYP.as_ref()).collect());
        let probecard_loadboard_typ_arr =
            mk_string_distinct(items.iter().map(|i| i.PROBECARD_LOADBOARD_TYP.as_ref()).collect());

        let sblot_id = if is_cp { sblot_id_arr.as_str() } else { item.SBLOT_ID.as_ref() };
        let wafer_no = if is_cp { item.WAFER_NO.as_ref() } else { wafer_no_arr.as_str() };

        // Calculate counts
        let standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();

        let input_cnt = (standard_ecids.len() + non_standard_ecids.len()) as i32;

        let pass_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();
        let pass_non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1))
            .map(|i| i.ECID.as_ref())
            .collect();

        let pass_cnt = (pass_standard_ecids.len() + pass_non_standard_ecids.len()) as i32;

        let fail_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();
        let fail_non_standard_ecids: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT.is_some() && i.TEST_RESULT.unwrap() != 1)
            .map(|i| i.ECID.as_ref())
            .collect();

        let fail_cnt = (fail_standard_ecids.len() + fail_non_standard_ecids.len()) as i32;

        let passbin_failing_item_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID == Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();
        let passbin_failing_item_non_standard: HashSet<&str> = items
            .iter()
            .filter(|i| i.IS_STANDARD_ECID != Some(1) && i.TEST_RESULT == Some(1) && i.HBIN_PF.as_ref() == PF_PASS)
            .map(|i| i.ECID.as_ref())
            .collect();

        let passbin_failingitem_cnt =
            (passbin_failing_item_standard.len() + passbin_failing_item_non_standard.len()) as i32;

        let exe_input_cnt = items.iter().filter(|i| i.TEST_VALUE.is_some()).count() as i32;

        let exe_pass_cnt = items.iter().filter(|i| i.TEST_RESULT == Some(1)).count() as i32;

        let exe_fail_cnt = items
            .iter()
            .filter(|i| i.TEST_RESULT.is_some() && i.TEST_RESULT != Some(1))
            .count() as i32;

        // Calculate time ranges - each field calculated independently as in Scala version
        let first_item = items
            .iter()
            .filter(|item| item.START_TIME.is_some())
            .min_by_key(|item| item.START_TIME)
            .ok_or_else(|| {
                panic!("No item with START_TIME found");
            })
            .unwrap();
        let last_item = items
            .iter()
            .filter(|item| item.END_TIME.is_some())
            .max_by_key(|item| item.END_TIME)
            .ok_or_else(|| {
                panic!("No item with END_TIME found");
            })
            .unwrap();

        let start_time_min = first_item.START_TIME;
        let start_hour_min = first_item.START_HOUR_KEY.as_ref();
        let start_day_min = first_item.START_DAY_KEY.as_ref();
        let end_time_max = last_item.END_TIME;
        let end_hour_max = last_item.END_HOUR_KEY.as_ref();
        let end_day_max = last_item.END_DAY_KEY.as_ref();

        // Check if we should calculate statistical metrics
        if (testitem_type == P || testitem_type == M) && !test_values.is_empty() {
            Self::new_with_statistics(
                data_source,
                items,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                &test_values,
                now,
            )
        } else {
            Self::new_without_statistics(
                data_source,
                item,
                product,
                product_type,
                product_family,
                is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                exe_input_cnt,
                exe_pass_cnt,
                exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                now,
            )
        }
    }

    /// Creates a new TestItemProgram with statistical calculations
    /// Used when testitem_type is P or M and test_values is not empty
    #[allow(clippy::too_many_arguments)]
    fn new_with_statistics(
        data_source: &str,
        items: &Vec<Arc<TestItemDetail>>,
        product: Arc<str>,
        product_type: Arc<str>,
        product_family: Arc<str>,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: &str,
        start_day_min: &str,
        end_time_max: Option<i64>,
        end_hour_max: &str,
        end_day_max: &str,
        create_hour_key: String,
        create_day_key: String,
        test_values: &[f64],
        now: DateTime<Utc>,
    ) -> Self {
        use crate::model::constant::ZERO;
        use crate::utils::stats::*;

        let item = &items[0];


        let latest_item = items
            .iter()
            .filter(|item| item.START_TIME.is_some())
            .max_by_key(|item| item.START_TIME)
            .ok_or_else(|| {
                log::error!("No item with START_TIME found for HI_LIMIT and LO_LIMIT");
            })
            .unwrap();

        let hi_latest = latest_item.HI_LIMIT;
        let lo_latest = latest_item.LO_LIMIT;
        let himax = hi_latest.unwrap_or(f64::NAN);
        let lomin = lo_latest.unwrap_or(f64::NAN);

        // Calculate basic statistics
        let median = calculate_median(test_values);
        let mean = calculate_mean(test_values);
        let max_val = calculate_max(test_values);
        let min_val = calculate_min(test_values);
        let square_sum = calculate_square_sum(test_values);
        let sum_val = calculate_sum(test_values);
        let std_dev_p = calculate_std_dev(test_values);
        let std_dev_s = if exe_input_cnt > 1 && square_sum.is_some() {
            // TODO 这里scala是没有开根号的
            to_decimal((square_sum.unwrap() / (exe_input_cnt - 1) as f64).sqrt())
        } else {
            to_decimal(0.0)
        };
        let range = if let (Some(max), Some(min)) = (max_val, min_val) { Some(max - min) } else { None };
        let q1 = quantile_sorted_q1(test_values);
        let q3 = quantile_sorted_q3(test_values);
        let p1 = quantile_sorted_1(test_values);
        let p5 = quantile_sorted_5(test_values);
        let p10 = quantile_sorted_10(test_values);
        let p90 = quantile_sorted_90(test_values);
        let p95 = quantile_sorted_95(test_values);
        let p99 = quantile_sorted_99(test_values);
        let iqr = if let (Some(q3_val), Some(q1_val)) = (q3, q1) { Some(q3_val - q1_val) } else { None };

        // Calculate process capability indices
        let (cp, cpu, cpl, cpk, ca) = if let (Some(mean_val), Some(std_dev_val)) = (mean, std_dev_p) {
            let cp_val = if hi_latest.is_some() && lo_latest.is_some() {
                to_decimal(calculate_cp(std_dev_val, lomin, himax))
            } else {
                None
            };

            let cpu_val =
                if hi_latest.is_some() { to_decimal(calculate_cpu(mean_val, std_dev_val, himax)) } else { None };

            let cpl_val =
                if lo_latest.is_some() { to_decimal(calculate_cpl(mean_val, std_dev_val, lomin)) } else { None };

            let cpk_val = if hi_latest.is_some() && lo_latest.is_some() {
                to_decimal(calculate_cpk(mean_val, std_dev_val, lomin, himax))
            } else {
                None
            };

            let ca_val = if himax.is_nan() || lomin.is_nan() || himax == lomin {
                None
            } else {
                to_decimal(calculate_ca(mean_val, lomin, himax))
            };

            (cp_val, cpu_val, cpl_val, cpk_val, ca_val)
        } else {
            (None, None, None, None, None)
        };

        // Calculate advanced statistics
        let skewness = calculate_skewness(test_values);
        let kurtosis = calculate_kurtosis(test_values);

        // Calculate outlier bounds and filtering
        let (lower_filter, upper_filter, max_wo, min_wo, outlier_cnt) = if let (Some(q1_val), Some(q3_val)) = (q1, q3) {
            let lower_filter = 2.5 * q1_val - 1.5 * q3_val;
            let upper_filter = 2.5 * q3_val - 1.5 * q1_val;
            let max_wo = if test_values.iter().any(|&v| v < upper_filter) {
                test_values
                    .iter()
                    .filter(|&&v| v < upper_filter)
                    .max_by(|a, b| a.partial_cmp(b).unwrap())
                    .copied()
            } else {
                None
            };
            let min_wo = if test_values.iter().any(|&v| v > lower_filter) {
                test_values
                    .iter()
                    .filter(|&&v| v > lower_filter)
                    .min_by(|a, b| a.partial_cmp(b).unwrap())
                    .copied()
            } else {
                None
            };
            let outlier_cnt = test_values.iter().filter(|&&v| v < lower_filter || v > upper_filter).count() as u32;
            (Some(lower_filter), Some(upper_filter), max_wo, min_wo, outlier_cnt)
        } else {
            (None, None, None, None, 0)
        };

        // Calculate normalization parameter
        let nor_parameter = if !himax.is_nan() && !lomin.is_nan() && himax + lomin == 0.0 {
            himax
        } else if himax.is_nan() || lomin.is_nan() {
            f64::NAN
        } else {
            (himax + lomin) / 2.0
        };
        let nor_flag = himax.is_nan() || lomin.is_nan() || nor_parameter.is_nan() || nor_parameter == 0.0;

        // Calculate normalization metrics
        let nor_median =
            if nor_flag || median.is_none() { None } else { Some((median.unwrap() - nor_parameter) / nor_parameter) };
        let nor_mean =
            if nor_flag || mean.is_none() { None } else { Some((mean.unwrap() - nor_parameter) / nor_parameter) };
        let nor_max =
            if nor_flag || max_val.is_none() { None } else { Some((max_val.unwrap() - nor_parameter) / nor_parameter) };
        let nor_min =
            if nor_flag || min_val.is_none() { None } else { Some((min_val.unwrap() - nor_parameter) / nor_parameter) };
        let nor_max_wo_outliers =
            if nor_flag || max_wo.is_none() { None } else { Some((max_wo.unwrap() - nor_parameter) / nor_parameter) };
        let nor_min_wo_outliers =
            if nor_flag || min_wo.is_none() { None } else { Some((min_wo.unwrap() - nor_parameter) / nor_parameter) };
        let nor_iqr =
            if nor_flag || iqr.is_none() { None } else { Some((iqr.unwrap() - nor_parameter) / nor_parameter) };
        let nor_q1 = if nor_flag || q1.is_none() { None } else { Some((q1.unwrap() - nor_parameter) / nor_parameter) };
        let nor_q3 = if nor_flag || q3.is_none() { None } else { Some((q3.unwrap() - nor_parameter) / nor_parameter) };
        let nor_lower = if nor_flag || lower_filter.is_none() {
            None
        } else {
            Some((lower_filter.unwrap() - nor_parameter) / nor_parameter)
        };
        let nor_upper = if nor_flag || upper_filter.is_none() {
            None
        } else {
            Some((upper_filter.unwrap() - nor_parameter) / nor_parameter)
        };

        // Calculate bounds for outlier detection
        let lower = if let (Some(lower_filter_val), max_wo_val) = (lower_filter, max_wo) {
            if let Some(max_wo_val) = max_wo_val {
                if lower_filter_val >= max_wo_val {
                    Some(lower_filter_val)
                } else {
                    Some(max_wo_val)
                }
            } else {
                Some(lower_filter_val)
            }
        } else {
            None
        };
        let upper = if let (Some(upper_filter_val), min_wo_val) = (upper_filter, min_wo) {
            if let Some(min_wo_val) = min_wo_val {
                if upper_filter_val <= min_wo_val {
                    Some(upper_filter_val)
                } else {
                    Some(min_wo_val)
                }
            } else {
                Some(upper_filter_val)
            }
        } else {
            None
        };

        // Create histogram (GROUP_DETAIL)
        let group_detail = group_detail(test_values, range, min_val);

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr),
            PRODUCT: product,
            PRODUCT_TYPE: product_type,
            PRODUCT_FAMILY: product_family,
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: item.IS_FINAL_TEST_IGNORE_TP.unwrap_or(1),
            UNITS_LIST: Arc::from(units_arr.as_str()),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr.as_str()),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr.as_str()),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr.as_str()),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr.as_str()),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr.as_str()),
            PROCESS_LIST: Arc::from(process_arr.as_str()),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr.as_str()),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr.as_str()),
            TESTER_NAME_LIST: Arc::from(tester_name_arr.as_str()),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr.as_str()),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr.as_str()),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr.as_str()),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr.as_str()),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr.as_str()),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            MEDIAN: median.and_then(to_decimal),
            MEAN: mean.and_then(to_decimal),
            MAX: max_val.and_then(to_decimal),
            MIN: min_val.and_then(to_decimal),
            MAX_WO_OUTLIERS: max_wo.and_then(to_decimal),
            MIN_WO_OUTLIERS: min_wo.and_then(to_decimal),
            SUM_SQ: square_sum.and_then(to_decimal),
            SUM_VALUE: sum_val.and_then(to_decimal),
            STDEV_P: std_dev_p.and_then(to_decimal),
            STDEV_S: std_dev_s,
            RANGE: range.and_then(to_decimal),
            IQR: iqr.and_then(to_decimal),
            Q1: q1.and_then(to_decimal),
            Q3: q3.and_then(to_decimal),
            LOWER: lower.and_then(to_decimal),
            UPPER: upper.and_then(to_decimal),
            OUTLIER_CNT: outlier_cnt,
            P1: p1.and_then(to_decimal),
            P5: p5.and_then(to_decimal),
            P10: p10.and_then(to_decimal),
            P90: p90.and_then(to_decimal),
            P95: p95.and_then(to_decimal),
            P99: p99.and_then(to_decimal),
            GROUP_DETAIL: group_detail.into_iter().collect(),
            PP: cp,
            PPU: cpu,
            PPL: cpl,
            PPK: cpk,
            CP: cp,
            CPU: cpu,
            CPL: cpl,
            CPK: cpk,
            CA: ca,
            SKEWNESS: skewness.and_then(to_decimal),
            KURTOSIS: kurtosis.and_then(to_decimal),
            NORMALIZATION_MEDIAN: nor_median.and_then(to_decimal),
            NORMALIZATION_MEAN: nor_mean.and_then(to_decimal),
            NORMALIZATION_MAX: nor_max.and_then(to_decimal),
            NORMALIZATION_MIN: nor_min.and_then(to_decimal),
            NORMALIZATION_MAX_WO_OUTLIERS: nor_max_wo_outliers.and_then(to_decimal),
            NORMALIZATION_MIN_WO_OUTLIERS: nor_min_wo_outliers.and_then(to_decimal),
            NORMALIZATION_IQR: nor_iqr.and_then(to_decimal),
            NORMALIZATION_Q1: nor_q1.and_then(to_decimal),
            NORMALIZATION_Q3: nor_q3.and_then(to_decimal),
            NORMALIZATION_LOWER: nor_lower.and_then(to_decimal),
            NORMALIZATION_UPPER: nor_upper.and_then(to_decimal),
            START_TIME: start_time_min.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            START_HOUR_KEY: Arc::from(start_hour_min),
            START_DAY_KEY: Arc::from(start_day_min),
            END_TIME: end_time_max.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            END_HOUR_KEY: Arc::from(end_hour_max),
            END_DAY_KEY: Arc::from(end_day_max),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key.as_str()),
            CREATE_DAY_KEY: Arc::from(create_day_key.as_str()),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: if item.UPLOAD_TIME == 0 {
                chrono::DateTime::from_timestamp_millis(item.CREATE_TIME).unwrap_or(now)
            } else {
                chrono::DateTime::from_timestamp_millis(item.UPLOAD_TIME).unwrap_or(now)
            },
            VERSION: item.VERSION,
            IS_DELETE: 0,
        }
    }

    /// Creates a new TestItemProgram without statistical calculations
    /// Used when testitem_type is not P or M, or when test_values is empty
    #[allow(clippy::too_many_arguments)]
    fn new_without_statistics(
        data_source: &str,
        item: &TestItemDetail,
        product: Arc<str>,
        product_type: Arc<str>,
        product_family: Arc<str>,
        is_pass_only: u8,
        sblot_id: &str,
        wafer_no: &str,
        wafer_id_arr: String,
        wafer_lot_id_arr: String,
        units_arr: String,
        origin_units_arr: String,
        lo_limit_arr: String,
        hi_limit_arr: String,
        origin_lo_limit_arr: String,
        origin_hi_limit_arr: String,
        process_arr: String,
        testitem_type_arr: String,
        test_temperature_arr: String,
        tester_name_arr: String,
        tester_type_arr: String,
        prober_handler_typ_arr: String,
        prober_handler_id_arr: String,
        probecard_loadboard_typ_arr: String,
        probecard_loadboard_id_arr: String,
        input_cnt: i32,
        pass_cnt: i32,
        fail_cnt: i32,
        passbin_failingitem_cnt: i32,
        exe_input_cnt: i32,
        exe_pass_cnt: i32,
        exe_fail_cnt: i32,
        start_time_min: Option<i64>,
        start_hour_min: &str,
        start_day_min: &str,
        end_time_max: Option<i64>,
        end_hour_max: &str,
        end_day_max: &str,
        create_hour_key: String,
        create_day_key: String,
        now: DateTime<Utc>,
    ) -> Self {
        use crate::model::constant::SYSTEM;

        Self {
            DATA_SOURCE: Arc::from(data_source),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: Arc::from(sblot_id),
            WAFER_ID: Arc::from(wafer_id_arr),
            WAFER_NO: Arc::from(wafer_no),
            WAFER_LOT_ID: Arc::from(wafer_lot_id_arr),
            PRODUCT: product,
            PRODUCT_TYPE: product_type,
            PRODUCT_FAMILY: product_family,
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: item.TEST_NUM,
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            IS_PASS_ONLY: is_pass_only,
            IS_FINAL: item.IS_FINAL_TEST_IGNORE_TP.unwrap_or(1),
            UNITS_LIST: Arc::from(units_arr.as_str()),
            ORIGIN_UNITS_LIST: Arc::from(origin_units_arr.as_str()),
            LO_LIMIT_LIST: Arc::from(lo_limit_arr.as_str()),
            HI_LIMIT_LIST: Arc::from(hi_limit_arr.as_str()),
            ORIGIN_LO_LIMIT_LIST: Arc::from(origin_lo_limit_arr.as_str()),
            ORIGIN_HI_LIMIT_LIST: Arc::from(origin_hi_limit_arr.as_str()),
            PROCESS_LIST: Arc::from(process_arr.as_str()),
            TESTITEM_TYPE_LIST: Arc::from(testitem_type_arr.as_str()),
            TEST_TEMPERATURE_LIST: Arc::from(test_temperature_arr.as_str()),
            TESTER_NAME_LIST: Arc::from(tester_name_arr.as_str()),
            TESTER_TYPE_LIST: Arc::from(tester_type_arr.as_str()),
            PROBER_HANDLER_TYP_LIST: Arc::from(prober_handler_typ_arr.as_str()),
            PROBER_HANDLER_ID_LIST: Arc::from(prober_handler_id_arr.as_str()),
            PROBECARD_LOADBOARD_TYP_LIST: Arc::from(probecard_loadboard_typ_arr.as_str()),
            PROBECARD_LOADBOARD_ID_LIST: Arc::from(probecard_loadboard_id_arr.as_str()),
            INPUT_CNT: input_cnt,
            PASS_CNT: pass_cnt,
            FAIL_CNT: fail_cnt,
            PASSBIN_FAILINGITEM_CNT: passbin_failingitem_cnt,
            EXE_INPUT_CNT: exe_input_cnt,
            EXE_PASS_CNT: exe_pass_cnt,
            EXE_FAIL_CNT: exe_fail_cnt,
            // All statistical fields are None for non-statistical types
            MEDIAN: None,
            MEAN: None,
            MAX: None,
            MIN: None,
            MAX_WO_OUTLIERS: None,
            MIN_WO_OUTLIERS: None,
            SUM_SQ: None,
            SUM_VALUE: None,
            STDEV_P: None,
            STDEV_S: None,
            RANGE: None,
            IQR: None,
            Q1: None,
            Q3: None,
            LOWER: None,
            UPPER: None,
            OUTLIER_CNT: 0,
            P1: None,
            P5: None,
            P10: None,
            P90: None,
            P95: None,
            P99: None,
            GROUP_DETAIL: Vec::new(),
            PP: None,
            PPU: None,
            PPL: None,
            PPK: None,
            CP: None,
            CPU: None,
            CPL: None,
            CPK: None,
            CA: None,
            SKEWNESS: None,
            KURTOSIS: None,
            NORMALIZATION_MEDIAN: None,
            NORMALIZATION_MEAN: None,
            NORMALIZATION_MAX: None,
            NORMALIZATION_MIN: None,
            NORMALIZATION_MAX_WO_OUTLIERS: None,
            NORMALIZATION_MIN_WO_OUTLIERS: None,
            NORMALIZATION_IQR: None,
            NORMALIZATION_Q1: None,
            NORMALIZATION_Q3: None,
            NORMALIZATION_LOWER: None,
            NORMALIZATION_UPPER: None,
            START_TIME: start_time_min.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            START_HOUR_KEY: Arc::from(start_hour_min),
            START_DAY_KEY: Arc::from(start_day_min),
            END_TIME: end_time_max.map(|t| DateTime::from_timestamp_millis(t).unwrap()),
            END_HOUR_KEY: Arc::from(end_hour_max),
            END_DAY_KEY: Arc::from(end_day_max),
            CREATE_TIME: now,
            CREATE_HOUR_KEY: Arc::from(create_hour_key.as_str()),
            CREATE_DAY_KEY: Arc::from(create_day_key.as_str()),
            CREATE_USER: Arc::from(SYSTEM),
            UPLOAD_TIME: if item.UPLOAD_TIME == 0 {
                chrono::DateTime::from_timestamp_millis(item.CREATE_TIME).unwrap_or(now)
            } else {
                chrono::DateTime::from_timestamp_millis(item.UPLOAD_TIME).unwrap_or(now)
            },
            VERSION: item.VERSION,
            IS_DELETE: 0,
        }
    }
}

/// Incremental aggregator for TestItemProgram
/// Allows streaming aggregation without storing all items in memory
#[derive(Debug)]
pub struct TestItemProgramAccumulator {
    // Base item for common fields (from first item)
    base_item: Option<Arc<TestItemDetail>>,

    // Configuration
    is_cp: bool,
    data_source: String,
    is_pass_only: u8,

    // Collections for distinct values
    sblot_ids: HashSet<Arc<str>>,
    wafer_nos: HashSet<Arc<str>>,
    wafer_ids: HashSet<Arc<str>>,
    wafer_lot_ids: HashSet<Arc<str>>,
    units: HashSet<Arc<str>>,
    origin_units: HashSet<Arc<str>>,
    lo_limits: HashSet<String>,
    hi_limits: HashSet<String>,
    origin_lo_limits: HashSet<String>,
    origin_hi_limits: HashSet<String>,
    processes: HashSet<Arc<str>>,
    testitem_types: HashSet<Arc<str>>,
    test_temperatures: HashSet<Arc<str>>,
    tester_names: HashSet<Arc<str>>,
    tester_types: HashSet<Arc<str>>,
    prober_handler_typs: HashSet<Arc<str>>,
    prober_handler_ids: HashSet<Arc<str>>,
    probecard_loadboard_typs: HashSet<Arc<str>>,
    probecard_loadboard_ids: HashSet<Arc<str>>,

    // Test values for statistical calculations
    test_values: Vec<f64>,

    // ECID sets for counting
    standard_ecids: HashSet<Arc<str>>,
    non_standard_ecids: HashSet<Arc<str>>,
    pass_standard_ecids: HashSet<Arc<str>>,
    pass_non_standard_ecids: HashSet<Arc<str>>,
    fail_standard_ecids: HashSet<Arc<str>>,
    fail_non_standard_ecids: HashSet<Arc<str>>,
    passbin_failing_item_standard: HashSet<Arc<str>>,
    passbin_failing_item_non_standard: HashSet<Arc<str>>,

    // Execution counts
    exe_input_cnt: i32,
    exe_pass_cnt: i32,
    exe_fail_cnt: i32,

    // Time tracking
    min_start_time: Option<i64>,
    min_start_hour_key: Option<Arc<str>>,
    min_start_day_key: Option<Arc<str>>,
    max_end_time: Option<i64>,
    max_end_hour_key: Option<Arc<str>>,
    max_end_day_key: Option<Arc<str>>,
}

impl TestItemProgramAccumulator {
    /// Create a new accumulator
    pub fn new(is_cp: bool, data_source: String, is_pass_only: u8) -> Self {
        Self {
            base_item: None,
            is_cp,
            data_source,
            is_pass_only,
            sblot_ids: HashSet::new(),
            wafer_nos: HashSet::new(),
            wafer_ids: HashSet::new(),
            wafer_lot_ids: HashSet::new(),
            units: HashSet::new(),
            origin_units: HashSet::new(),
            lo_limits: HashSet::new(),
            hi_limits: HashSet::new(),
            origin_lo_limits: HashSet::new(),
            origin_hi_limits: HashSet::new(),
            processes: HashSet::new(),
            testitem_types: HashSet::new(),
            test_temperatures: HashSet::new(),
            tester_names: HashSet::new(),
            tester_types: HashSet::new(),
            prober_handler_typs: HashSet::new(),
            prober_handler_ids: HashSet::new(),
            probecard_loadboard_typs: HashSet::new(),
            probecard_loadboard_ids: HashSet::new(),
            test_values: Vec::new(),
            standard_ecids: HashSet::new(),
            non_standard_ecids: HashSet::new(),
            pass_standard_ecids: HashSet::new(),
            pass_non_standard_ecids: HashSet::new(),
            fail_standard_ecids: HashSet::new(),
            fail_non_standard_ecids: HashSet::new(),
            passbin_failing_item_standard: HashSet::new(),
            passbin_failing_item_non_standard: HashSet::new(),
            exe_input_cnt: 0,
            exe_pass_cnt: 0,
            exe_fail_cnt: 0,
            min_start_time: None,
            min_start_hour_key: None,
            min_start_day_key: None,
            max_end_time: None,
            max_end_hour_key: None,
            max_end_day_key: None,
        }
    }

    /// Update accumulator with a new item
    pub fn update(&mut self, item: &Arc<TestItemDetail>) {
        // Set base item from first item
        if self.base_item.is_none() {
            self.base_item = Some(item.clone());
        }

        // Collect distinct values
        self.sblot_ids.insert(item.SBLOT_ID.clone());
        self.wafer_nos.insert(item.WAFER_NO.clone());
        self.wafer_ids.insert(item.WAFER_ID.clone());
        self.wafer_lot_ids.insert(item.WAFER_LOT_ID.clone());
        self.units.insert(item.UNITS.clone());
        self.origin_units.insert(item.ORIGIN_UNITS.clone());

        if let Some(lo_limit) = &item.LO_LIMIT {
            self.lo_limits.insert(lo_limit.to_string());
        }
        if let Some(hi_limit) = &item.HI_LIMIT {
            self.hi_limits.insert(hi_limit.to_string());
        }
        if let Some(origin_lo_limit) = &item.ORIGIN_LO_LIMIT {
            self.origin_lo_limits.insert(origin_lo_limit.to_string());
        }
        if let Some(origin_hi_limit) = &item.ORIGIN_HI_LIMIT {
            self.origin_hi_limits.insert(origin_hi_limit.to_string());
        }

        self.processes.insert(item.PROCESS.clone());
        self.testitem_types.insert(item.TESTITEM_TYPE.clone());
        self.test_temperatures.insert(item.TEST_TEMPERATURE.clone());
        self.tester_names.insert(item.TESTER_NAME.clone());
        self.tester_types.insert(item.TESTER_TYPE.clone());
        self.prober_handler_typs.insert(item.PROBER_HANDLER_TYP.clone());
        self.prober_handler_ids.insert(item.PROBER_HANDLER_ID.clone());
        self.probecard_loadboard_typs.insert(item.PROBECARD_LOADBOARD_TYP.clone());
        self.probecard_loadboard_ids.insert(item.PROBECARD_LOADBOARD_ID.clone());

        // Collect test values for statistics
        if let (Some(test_value), Some(test_result)) = (item.TEST_VALUE, item.TEST_RESULT) {
            if test_result < 2 {
                self.test_values.push(test_value);
            }
        }

        // Track ECIDs for counting
        let is_standard = item.IS_STANDARD_ECID == Some(1);
        if is_standard {
            self.standard_ecids.insert(item.ECID.clone());
            if item.TEST_RESULT == Some(1) {
                self.pass_standard_ecids.insert(item.ECID.clone());
                if item.HBIN_PF.as_ref() == PF_PASS {
                    self.passbin_failing_item_standard.insert(item.ECID.clone());
                }
            } else if item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1 {
                self.fail_standard_ecids.insert(item.ECID.clone());
            }
        } else {
            self.non_standard_ecids.insert(item.ECID.clone());
            if item.TEST_RESULT == Some(1) {
                self.pass_non_standard_ecids.insert(item.ECID.clone());
                if item.HBIN_PF.as_ref() == PF_PASS {
                    self.passbin_failing_item_non_standard.insert(item.ECID.clone());
                }
            } else if item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1 {
                self.fail_non_standard_ecids.insert(item.ECID.clone());
            }
        }

        // Track execution counts
        if item.TEST_VALUE.is_some() {
            self.exe_input_cnt += 1;
        }
        if item.TEST_RESULT == Some(1) {
            self.exe_pass_cnt += 1;
        }
        if item.TEST_RESULT.is_some() && item.TEST_RESULT != Some(1) {
            self.exe_fail_cnt += 1;
        }

        // Track time ranges
        if let Some(start_time) = item.START_TIME {
            if self.min_start_time.is_none() || start_time < self.min_start_time.unwrap() {
                self.min_start_time = Some(start_time);
                self.min_start_hour_key = Some(item.START_HOUR_KEY.clone());
                self.min_start_day_key = Some(item.START_DAY_KEY.clone());
            }
        }

        if let Some(end_time) = item.END_TIME {
            if self.max_end_time.is_none() || end_time > self.max_end_time.unwrap() {
                self.max_end_time = Some(end_time);
                self.max_end_hour_key = Some(item.END_HOUR_KEY.clone());
                self.max_end_day_key = Some(item.END_DAY_KEY.clone());
            }
        }
    }

    /// Collect final TestItemProgram result from accumulated data
    pub fn collect(mut self, product_list: &[crate::dto::ods::product_config::OdsProductConfig]) -> TestItemProgram {
        let base_item = self.base_item.expect("No items were added to accumulator");

        // Sort test values for statistical calculations
        self.test_values.sort_by(|a, b| a.partial_cmp(b).unwrap());

        // Get product information
        let (product, product_type, product_family) = if !product_list.is_empty() {
            let product_info = &product_list[0];
            (
                product_info.PRODUCT.clone(),
                product_info.PRODUCT_TYPE.clone(),
                product_info.PRODUCT_FAMILY.clone(),
            )
        } else {
            (Arc::from(EMPTY), Arc::from(EMPTY), Arc::from(EMPTY))
        };

        let now = Utc::now();
        let create_hour_key = get_day_hour(now);
        let create_day_key = get_day(now);

        // Build distinct string arrays
        let sblot_id_arr = mk_string_distinct(self.sblot_ids.iter().map(|s| s.as_ref()).collect());
        let wafer_no_arr = mk_string_distinct(self.wafer_nos.iter().map(|s| s.as_ref()).collect());
        let wafer_id_arr = mk_string_distinct(self.wafer_ids.iter().map(|s| s.as_ref()).collect());
        let wafer_lot_id_arr = mk_string_distinct(self.wafer_lot_ids.iter().map(|s| s.as_ref()).collect());
        let units_arr = mk_string_distinct(self.units.iter().map(|s| s.as_ref()).collect());
        let origin_units_arr = mk_string_distinct(self.origin_units.iter().map(|s| s.as_ref()).collect());
        let lo_limit_arr = mk_string_distinct(self.lo_limits.iter().map(|s| s.as_str()).collect());
        let hi_limit_arr = mk_string_distinct(self.hi_limits.iter().map(|s| s.as_str()).collect());
        let origin_lo_limit_arr = mk_string_distinct(self.origin_lo_limits.iter().map(|s| s.as_str()).collect());
        let origin_hi_limit_arr = mk_string_distinct(self.origin_hi_limits.iter().map(|s| s.as_str()).collect());
        let process_arr = mk_string_distinct(self.processes.iter().map(|s| s.as_ref()).collect());
        let testitem_type_arr = mk_string_distinct(self.testitem_types.iter().map(|s| s.as_ref()).collect());
        let test_temperature_arr = mk_string_distinct(self.test_temperatures.iter().map(|s| s.as_ref()).collect());
        let tester_name_arr = mk_string_distinct(self.tester_names.iter().map(|s| s.as_ref()).collect());
        let tester_type_arr = mk_string_distinct(self.tester_types.iter().map(|s| s.as_ref()).collect());
        let prober_handler_typ_arr = mk_string_distinct(self.prober_handler_typs.iter().map(|s| s.as_ref()).collect());
        let prober_handler_id_arr = mk_string_distinct(self.prober_handler_ids.iter().map(|s| s.as_ref()).collect());
        let probecard_loadboard_typ_arr = mk_string_distinct(self.probecard_loadboard_typs.iter().map(|s| s.as_ref()).collect());
        let probecard_loadboard_id_arr = mk_string_distinct(self.probecard_loadboard_ids.iter().map(|s| s.as_ref()).collect());

        // Calculate field values based on is_cp flag
        let sblot_id = if self.is_cp { sblot_id_arr.as_str() } else { base_item.SBLOT_ID.as_ref() };
        let wafer_no = if self.is_cp { base_item.WAFER_NO.as_ref() } else { wafer_no_arr.as_str() };

        // Calculate counts
        let input_cnt = (self.standard_ecids.len() + self.non_standard_ecids.len()) as i32;
        let pass_cnt = (self.pass_standard_ecids.len() + self.pass_non_standard_ecids.len()) as i32;
        let fail_cnt = (self.fail_standard_ecids.len() + self.fail_non_standard_ecids.len()) as i32;
        let passbin_failingitem_cnt = (self.passbin_failing_item_standard.len() + self.passbin_failing_item_non_standard.len()) as i32;

        // Get time values
        let start_time_min = self.min_start_time;
        let start_hour_min = self.min_start_hour_key.as_ref().map(|s| s.as_ref()).unwrap_or(EMPTY);
        let start_day_min = self.min_start_day_key.as_ref().map(|s| s.as_ref()).unwrap_or(EMPTY);
        let end_time_max = self.max_end_time;
        let end_hour_max = self.max_end_hour_key.as_ref().map(|s| s.as_ref()).unwrap_or(EMPTY);
        let end_day_max = self.max_end_day_key.as_ref().map(|s| s.as_ref()).unwrap_or(EMPTY);

        // Check if we should calculate statistical metrics
        let testitem_type = base_item.TESTITEM_TYPE.as_ref();
        if (testitem_type == P || testitem_type == M) && !self.test_values.is_empty() {
            // Create a temporary items vector for the existing statistical calculation method
            // This is a compromise to reuse existing complex statistical logic
            let items = vec![base_item.clone()]; // We only need one item for the statistical method signature

            TestItemProgram::new_with_statistics(
                &self.data_source,
                &items,
                product,
                product_type,
                product_family,
                self.is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                self.exe_input_cnt,
                self.exe_pass_cnt,
                self.exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                &self.test_values,
                now,
            )
        } else {
            // Create without statistical metrics
            TestItemProgram::new_without_statistics(
                &self.data_source,
                &base_item,
                product,
                product_type,
                product_family,
                self.is_pass_only,
                sblot_id,
                wafer_no,
                wafer_id_arr,
                wafer_lot_id_arr,
                units_arr,
                origin_units_arr,
                lo_limit_arr,
                hi_limit_arr,
                origin_lo_limit_arr,
                origin_hi_limit_arr,
                process_arr,
                testitem_type_arr,
                test_temperature_arr,
                tester_name_arr,
                tester_type_arr,
                prober_handler_typ_arr,
                prober_handler_id_arr,
                probecard_loadboard_typ_arr,
                probecard_loadboard_id_arr,
                input_cnt,
                pass_cnt,
                fail_cnt,
                passbin_failingitem_cnt,
                self.exe_input_cnt,
                self.exe_pass_cnt,
                self.exe_fail_cnt,
                start_time_min,
                start_hour_min,
                start_day_min,
                end_time_max,
                end_hour_max,
                end_day_max,
                create_hour_key,
                create_day_key,
                now,
            )
        }
    }
}
