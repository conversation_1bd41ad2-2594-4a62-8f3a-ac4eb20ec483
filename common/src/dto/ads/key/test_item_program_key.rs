use serde::{Deserialize, Serialize};
use std::hash::{<PERSON>h, <PERSON><PERSON>};
use std::sync::Arc;
use crate::dto::ads::value::test_item_detail;
use crate::model::constant::test_area::TestArea;
use std::sync::OnceLock;
use std::collections::HashSet;

/// Static HashSet of CP test area strings for efficient lookup
fn cp_test_area_set() -> &'static HashSet<&'static str> {
    static CP_TEST_AREA_SET: OnceLock<HashSet<&'static str>> = OnceLock::new();
    CP_TEST_AREA_SET.get_or_init(|| {
        TestArea::get_cp_list()
            .iter()
            .map(|area| area.get_area())
            .collect()
    })
}

/// Aggregation key for program-level test item grouping
/// Groups test items by customer, device, lot, test program, and test item details
/// Replicated from Scala case class TestItemProgramKey with all fields
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[allow(non_snake_case)]
pub struct TestItemProgramKey {
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
}

impl TestItemProgramKey {
    /// Creates a new TestItemProgramKey
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        upload_type: Arc<str>,
        customer: Arc<str>,
        sub_customer: Arc<str>,
        fab: Arc<str>,
        fab_site: Arc<str>,
        factory: Arc<str>,
        factory_site: Arc<str>,
        test_area: Arc<str>,
        test_stage: Arc<str>,
        device_id: Arc<str>,
        lot_type: Arc<str>,
        lot_id: Arc<str>,
        sblot_id: Arc<str>,
        test_program: Arc<str>,
        test_program_version: Arc<str>,
        test_num: Option<i64>,
        test_txt: Arc<str>,
        test_item: Arc<str>,
        is_final_test_ignore_tp: Option<i32>,
    ) -> Self {
        Self {
            UPLOAD_TYPE: upload_type,
            CUSTOMER: customer,
            SUB_CUSTOMER: sub_customer,
            FAB: fab,
            FAB_SITE: fab_site,
            FACTORY: factory,
            FACTORY_SITE: factory_site,
            TEST_AREA: test_area,
            TEST_STAGE: test_stage,
            DEVICE_ID: device_id,
            LOT_TYPE: lot_type,
            LOT_ID: lot_id,
            SBLOT_ID: sblot_id,
            TEST_PROGRAM: test_program,
            TEST_PROGRAM_VERSION: test_program_version,
            TEST_NUM: test_num,
            TEST_TXT: test_txt,
            TEST_ITEM: test_item,
            IS_FINAL_TEST_IGNORE_TP: is_final_test_ignore_tp,
        }
    }

    /// Creates a TestItemProgramKey from a TestItemDetail
    pub fn from_test_item_detail(detail: &test_item_detail::TestItemDetail, is_cp: bool) -> Self {
        // Determine SBLOT_ID based on test area
        let sblot_id = {
            // Check if the test area is a CP test area using the static set
            if is_cp {
                detail.WAFER_NO.clone()
            } else {
                detail.SBLOT_ID.clone()
            }
        };

        Self {
            UPLOAD_TYPE: detail.UPLOAD_TYPE.clone(),
            CUSTOMER: detail.CUSTOMER.clone(),
            SUB_CUSTOMER: detail.SUB_CUSTOMER.clone(),
            FAB: detail.FAB.clone(),
            FAB_SITE: detail.FAB_SITE.clone(),
            FACTORY: detail.FACTORY.clone(),
            FACTORY_SITE: detail.FACTORY_SITE.clone(),
            TEST_AREA: detail.TEST_AREA.clone(),
            TEST_STAGE: detail.TEST_STAGE.clone(),
            DEVICE_ID: detail.DEVICE_ID.clone(),
            LOT_TYPE: detail.LOT_TYPE.clone(),
            LOT_ID: detail.LOT_ID.clone(),
            SBLOT_ID: sblot_id,
            TEST_PROGRAM: detail.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: detail.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: detail.TEST_NUM.map(|n| n as i64),
            TEST_TXT: detail.TEST_TXT.clone(),
            TEST_ITEM: detail.TEST_ITEM.clone(),
            IS_FINAL_TEST_IGNORE_TP: detail.IS_FINAL_TEST_IGNORE_TP.map(|n| n as i32),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_test_item_program_key_creation() {
        let key = TestItemProgramKey::new(
            "STDF".into(),
            "CUSTOMER1".into(),
            "SUB_CUSTOMER1".into(),
            "FAB1".into(),
            "FAB_SITE1".into(),
            "FACTORY1".into(),
            "FACTORY_SITE1".into(),
            "CP".into(),
            "CP1".into(),
            "DEVICE1".into(),
            "PRODUCTION".into(),
            "LOT1".into(),
            "SBLOT1".into(),
            "PROGRAM1".into(),
            "V1.0".into(),
            Some(100),
            "TEST_TXT1".into(),
            "TEST_ITEM1".into(),
            Some(1),
        );

        assert_eq!(key.UPLOAD_TYPE.as_ref(), "STDF");
        assert_eq!(key.CUSTOMER.as_ref(), "CUSTOMER1");
        assert_eq!(key.TEST_NUM, Some(100));
        assert_eq!(key.IS_FINAL_TEST_IGNORE_TP, Some(1));
    }

    #[test]
    fn test_test_item_program_key_hash() {
        let key1 = TestItemProgramKey::new(
            "STDF".into(),
            "CUSTOMER1".into(),
            "SUB_CUSTOMER1".into(),
            "FAB1".into(),
            "FAB_SITE1".into(),
            "FACTORY1".into(),
            "FACTORY_SITE1".into(),
            "CP".into(),
            "CP1".into(),
            "DEVICE1".into(),
            "PRODUCTION".into(),
            "LOT1".into(),
            "SBLOT1".into(),
            "PROGRAM1".into(),
            "V1.0".into(),
            Some(100),
            "TEST_TXT1".into(),
            "TEST_ITEM1".into(),
            Some(1),
        );

        let key2 = key1.clone();

        let mut map = HashMap::new();
        map.insert(key1, "value1");
        assert!(map.contains_key(&key2));
    }

    #[test]
    fn test_test_item_program_key_serialization() {
        let key = TestItemProgramKey::new(
            "STDF".into(),
            "CUSTOMER1".into(),
            "SUB_CUSTOMER1".into(),
            "FAB1".into(),
            "FAB_SITE1".into(),
            "FACTORY1".into(),
            "FACTORY_SITE1".into(),
            "CP".into(),
            "CP1".into(),
            "DEVICE1".into(),
            "PRODUCTION".into(),
            "LOT1".into(),
            "SBLOT1".into(),
            "PROGRAM1".into(),
            "V1.0".into(),
            Some(100),
            "TEST_TXT1".into(),
            "TEST_ITEM1".into(),
            Some(1),
        );

        // Test serialization
        let serialized = serde_json::to_string(&key).expect("Failed to serialize");
        assert!(serialized.contains("CUSTOMER1"));
        assert!(serialized.contains("STDF"));

        // Test deserialization
        let deserialized: TestItemProgramKey = serde_json::from_str(&serialized).expect("Failed to deserialize");
        assert_eq!(key, deserialized);
    }
}
