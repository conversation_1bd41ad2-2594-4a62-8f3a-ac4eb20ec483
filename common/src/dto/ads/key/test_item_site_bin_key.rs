use serde::{Deserialize, Serialize};
use std::hash::{Hash, <PERSON><PERSON>};
use std::sync::Arc;

/// Aggregation key for site-bin combined test item grouping
/// Combines both site and bin dimensions for comprehensive grouping
/// Replicated from Scala case class TestItemSiteBinKey with all fields
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[allow(non_snake_case)]
pub struct TestItemSiteBinKey {
    pub UPLOAD_TYPE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub FAB: Arc<str>,
    pub FAB_SITE: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub LOT_TYPE: Arc<str>,
    pub LOT_ID: Arc<str>,
    pub SBLOT_ID: Arc<str>,
    pub TEST_PROGRAM: Arc<str>,
    pub TEST_PROGRAM_VERSION: Arc<str>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Arc<str>,
    pub TEST_ITEM: Arc<str>,
    pub SITE: Option<i64>,
    pub HBIN: Arc<str>,
    pub SBIN: Arc<str>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_NAM: Arc<str>,
    pub HBIN_PF: Arc<str>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_NAM: Arc<str>,
    pub SBIN_PF: Arc<str>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
}


impl TestItemSiteBinKey {
    /// Creates a new TestItemSiteBinKey
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        upload_type: Arc<str>,
        customer: Arc<str>,
        sub_customer: Arc<str>,
        fab: Arc<str>,
        fab_site: Arc<str>,
        factory: Arc<str>,
        factory_site: Arc<str>,
        test_area: Arc<str>,
        test_stage: Arc<str>,
        device_id: Arc<str>,
        lot_type: Arc<str>,
        lot_id: Arc<str>,
        sblot_id: Arc<str>,
        test_program: Arc<str>,
        test_program_version: Arc<str>,
        test_num: Option<i64>,
        test_txt: Arc<str>,
        test_item: Arc<str>,
        site: Option<i64>,
        hbin: Arc<str>,
        sbin: Arc<str>,
        hbin_num: Option<i64>,
        hbin_nam: Arc<str>,
        hbin_pf: Arc<str>,
        sbin_num: Option<i64>,
        sbin_nam: Arc<str>,
        sbin_pf: Arc<str>,
        is_final_test_ignore_tp: Option<i32>,
    ) -> Self {
        Self {
            UPLOAD_TYPE: upload_type,
            CUSTOMER: customer,
            SUB_CUSTOMER: sub_customer,
            FAB: fab,
            FAB_SITE: fab_site,
            FACTORY: factory,
            FACTORY_SITE: factory_site,
            TEST_AREA: test_area,
            TEST_STAGE: test_stage,
            DEVICE_ID: device_id,
            LOT_TYPE: lot_type,
            LOT_ID: lot_id,
            SBLOT_ID: sblot_id,
            TEST_PROGRAM: test_program,
            TEST_PROGRAM_VERSION: test_program_version,
            TEST_NUM: test_num,
            TEST_TXT: test_txt,
            TEST_ITEM: test_item,
            SITE: site,
            HBIN: hbin,
            SBIN: sbin,
            HBIN_NUM: hbin_num,
            HBIN_NAM: hbin_nam,
            HBIN_PF: hbin_pf,
            SBIN_NUM: sbin_num,
            SBIN_NAM: sbin_nam,
            SBIN_PF: sbin_pf,
            IS_FINAL_TEST_IGNORE_TP: is_final_test_ignore_tp,
        }
    }

    /// Creates a TestItemSiteBinKey from a TestItemDetail
    pub fn from_test_item_detail(detail: &crate::dto::ads::value::test_item_detail::TestItemDetail, is_cp: bool) -> Self {
        // Determine SBLOT_ID based on test area
        let sblot_id = {
            // Check if the test area is a CP test area using the static set
            if is_cp {
                detail.WAFER_NO.clone()
            } else {
                detail.SBLOT_ID.clone()
            }
        };
        Self {
            UPLOAD_TYPE: detail.UPLOAD_TYPE.clone(),
            CUSTOMER: detail.CUSTOMER.clone(),
            SUB_CUSTOMER: detail.SUB_CUSTOMER.clone(),
            FAB: detail.FAB.clone(),
            FAB_SITE: detail.FAB_SITE.clone(),
            FACTORY: detail.FACTORY.clone(),
            FACTORY_SITE: detail.FACTORY_SITE.clone(),
            TEST_AREA: detail.TEST_AREA.clone(),
            TEST_STAGE: detail.TEST_STAGE.clone(),
            DEVICE_ID: detail.DEVICE_ID.clone(),
            LOT_TYPE: detail.LOT_TYPE.clone(),
            LOT_ID: detail.LOT_ID.clone(),
            SBLOT_ID: sblot_id,
            TEST_PROGRAM: detail.TEST_PROGRAM.clone(),
            TEST_PROGRAM_VERSION: detail.TEST_PROGRAM_VERSION.clone(),
            TEST_NUM: detail.TEST_NUM.map(|n| n as i64),
            TEST_TXT: detail.TEST_TXT.clone(),
            TEST_ITEM: detail.TEST_ITEM.clone(),
            SITE: detail.SITE.map(|s| s as i64),
            HBIN: detail.HBIN.clone(),
            SBIN: detail.SBIN.clone(),
            HBIN_NUM: detail.HBIN_NUM.map(|n| n as i64),
            HBIN_NAM: detail.HBIN_NAM.clone(),
            HBIN_PF: detail.HBIN_PF.clone(),
            SBIN_NUM: detail.SBIN_NUM.map(|n| n as i64),
            SBIN_NAM: detail.SBIN_NAM.clone(),
            SBIN_PF: detail.SBIN_PF.clone(),
            IS_FINAL_TEST_IGNORE_TP: detail.IS_FINAL_TEST_IGNORE_TP.map(|n| n as i32),
        }
    }
}
