use serde::{Deserialize, Serialize};
use std::sync::Arc;
use clickhouse::Row;

/// ODS Product Configuration structure
/// Contains product information for aggregation
/// Corresponds to the Scala OdsProductConfig case class
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Row)]
#[allow(non_snake_case)]
pub struct OdsProductConfig {
    pub DATA_SOURCE: Arc<str>,
    pub CUSTOMER: Arc<str>,
    pub SUB_CUSTOMER: Arc<str>,
    pub FACTORY: Arc<str>,
    pub FACTORY_SITE: Arc<str>,
    pub TEST_AREA: Arc<str>,
    pub TEST_STAGE: Arc<str>,
    pub DEVICE_ID: Arc<str>,
    pub PRODUCT: Arc<str>,
    pub PRODUCT_TYPE: Arc<str>,
    pub PRODUCT_FAMILY: Arc<str>,
}

impl OdsProductConfig {
    pub fn new() -> Self {
        Self {
            DATA_SOURCE: "".into(),
            CUSTOMER: "".into(),
            SUB_CUSTOMER: "".into(),
            FACTORY: "".into(),
            FACTORY_SITE: "".into(),
            TEST_AREA: "".into(),
            TEST_STAGE: "".into(),
            DEVICE_ID: "".into(),
            PRODUCT: "".into(),
            PRODUCT_TYPE: "".into(),
            PRODUCT_FAMILY: "".into(),
        }
    }
}

impl Default for OdsProductConfig {
    fn default() -> Self {
        Self::new()
    }
}
