use clickhouse::Row;
use serde::{Deserialize, Serialize};
use arrow::record_batch::RecordBatch;
use serde_arrow::schema::{TracingOptions, SchemaLike};
use arrow::datatypes::{Field, FieldRef};
use std::sync::Arc;
use parquet_provider::RecordBatchWrapper;

#[derive(Row, Debug, Clone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct BinFailitemIndex {
    pub CUSTOMER: Option<String>,
    pub SUB_CUSTOMER: Option<String>,
    pub UPLOAD_TYPE: Option<String>,
    pub FACTORY: Option<String>,
    pub FACTORY_SITE: Option<String>,
    pub FAB: Option<String>,
    pub FAB_SITE: Option<String>,
    pub TEST_AREA: Option<String>,
    pub TEST_STAGE: Option<String>,
    pub LOT_TYPE: Option<String>,
    pub DEVICE_ID: Option<String>,
    pub LOT_ID: Option<String>,
    pub SBLOT_ID: Option<String>,
    pub WAFER_LOT_ID: Option<String>,
    pub WAFER_ID: Option<String>,
    pub WAFER_ID_KEY: Option<String>,
    pub WAFER_NO: Option<String>,
    pub WAFER_NO_KEY: Option<String>,
    pub TEST_PROGRAM: Option<String>,
    pub TEST_TEMPERATURE: Option<String>,
    pub TEST_PROGRAM_VERSION: Option<String>,
    pub FILE_ID: Option<i64>,
    pub FILE_NAME: Option<String>,
    pub FILE_TYPE: Option<String>,
    pub TESTER_NAME: Option<String>,
    pub TESTER_TYPE: Option<String>,
    pub PROBER_HANDLER_ID: Option<String>,
    pub PROBECARD_LOADBOARD_ID: Option<String>,
    pub START_TIME: Option<i64>,
    pub END_TIME: Option<i64>,
    pub START_HOUR_KEY: Option<String>,
    pub START_DAY_KEY: Option<String>,
    pub END_HOUR_KEY: Option<String>,
    pub END_DAY_KEY: Option<String>,
    pub FLOW_ID: Option<String>,
    pub FINAL_FLAG: Option<i32>,
    pub HBIN_NUM: Option<i64>,
    pub HBIN_NUM_KEY: Option<String>,
    pub HBIN_NAM: Option<String>,
    pub HBIN_PF: Option<String>,
    pub HBIN: Option<String>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_NUM_KEY: Option<String>,
    pub SBIN_NAM: Option<String>,
    pub SBIN_PF: Option<String>,
    pub SBIN: Option<String>,
    pub FIRST_FAIL_ITEM_CNT: Option<i64>,
    pub FIRST_FAIL_ITEM_DETAIL: Option<String>,
    pub LAST_FAIL_ITEM_OF_ALL: Option<String>,
    pub ALL_FAIL_ITEM_CNT: Option<i64>,
    pub ALL_FAIL_ITEM_DETAIL: Option<String>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_TIME: Option<i64>,
    pub CREATE_USER: Option<String>,
    pub VERSION: Option<i64>,
    pub PROCESS: Option<String>,
    pub UPLOAD_TIME: Option<i64>,
}

impl BinFailitemIndex {
    pub fn new() -> Self {
        Self {
            CUSTOMER: None,
            SUB_CUSTOMER: None,
            UPLOAD_TYPE: None,
            FACTORY: None,
            FACTORY_SITE: None,
            FAB: None,
            FAB_SITE: None,
            TEST_AREA: None,
            TEST_STAGE: None,
            LOT_TYPE: None,
            DEVICE_ID: None,
            LOT_ID: None,
            SBLOT_ID: None,
            WAFER_LOT_ID: None,
            WAFER_ID: None,
            WAFER_ID_KEY: None,
            WAFER_NO: None,
            WAFER_NO_KEY: None,
            TEST_PROGRAM: None,
            TEST_TEMPERATURE: None,
            TEST_PROGRAM_VERSION: None,
            FILE_ID: None,
            FILE_NAME: None,
            FILE_TYPE: None,
            TESTER_NAME: None,
            TESTER_TYPE: None,
            PROBER_HANDLER_ID: None,
            PROBECARD_LOADBOARD_ID: None,
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: None,
            START_DAY_KEY: None,
            END_HOUR_KEY: None,
            END_DAY_KEY: None,
            FLOW_ID: None,
            FINAL_FLAG: None,
            HBIN_NUM: None,
            HBIN_NUM_KEY: None,
            HBIN_NAM: None,
            HBIN_PF: None,
            HBIN: None,
            SBIN_NUM: None,
            SBIN_NUM_KEY: None,
            SBIN_NAM: None,
            SBIN_PF: None,
            SBIN: None,
            FIRST_FAIL_ITEM_CNT: None,
            FIRST_FAIL_ITEM_DETAIL: None,
            LAST_FAIL_ITEM_OF_ALL: None,
            ALL_FAIL_ITEM_CNT: None,
            ALL_FAIL_ITEM_DETAIL: None,
            CREATE_HOUR_KEY: None,
            CREATE_DAY_KEY: None,
            CREATE_TIME: None,
            CREATE_USER: None,
            VERSION: None,
            PROCESS: None,
            UPLOAD_TIME: None,
        }
    }
}

impl RecordBatchWrapper for BinFailitemIndex {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<BinFailitemIndex> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &[Self]) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }

        let fields: Vec<Arc<Field>> = Vec::<FieldRef>::from_type::<BinFailitemIndex>(
            TracingOptions::default()
                .allow_null_fields(true)
                .map_as_struct(false)
                .strings_as_large_utf8(false),
        )
        .map_err(|e| format!("Failed to create schema from samples: {}", e))?;

        let arrays = serde_arrow::to_arrow(&fields, data)
            .map_err(|e| format!("Failed to convert to arrow arrays: {}", e))?;
        let schema = arrow::datatypes::Schema::new(fields);

        RecordBatch::try_new(Arc::new(schema), arrays)
            .map_err(|e| format!("Failed to create RecordBatch: {}", e))
    }
}
