//! SiteTestItemIndex的ClickHouse Sink Handler实现

use crate::ck::ck_sink::SinkHandler;
use crate::model::dw_table_enum::DwTableEnum;

/// SiteTestItemIndex的ClickHouse Sink Handler
/// 对应Scala版本的SiteTestItemIndexHandler
pub struct SiteTestItemIndexHandler {
    db_name: String,
    table_name: String,
    insert_cluster_table: bool,
}

impl SiteTestItemIndexHandler {
    pub fn new(db_name: String, insert_cluster_table: bool) -> Self {
        // 根据insert_cluster_table参数决定表名
        let table_name = if insert_cluster_table {
            DwTableEnum::DwsSiteTestItemIndex.get_cluster_table()
        } else {
            DwTableEnum::DwsSiteTestItemIndex.get_local_table()
        };

        Self { db_name, table_name, insert_cluster_table }
    }

    /// 为了兼容性，提供一个只接受db_name的构造函数，默认使用本地表
    pub fn new_local(db_name: String) -> Self {
        Self::new(db_name, false)
    }
}

impl SinkHandler for SiteTestItemIndexHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        // 与Scala版本保持一致的分区表达式
        "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{LOT_ID}', '{WAFER_ID}')"
    }
}
