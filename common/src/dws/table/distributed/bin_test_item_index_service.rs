//! BinTestItemIndexService 实现
//!
//! 对应Scala中的BinTestItemIndexService类，用于计算Bin测试项索引

use crate::dws::dws_service::{DwsService, DwsSubTestItemDetail};
use crate::utils::{date, stats};
use crate::utils::decimal::IntoDecimal38_18;
use chrono::DateTime;
use log::info;
use std::collections::HashMap;
use std::sync::Arc;
use rayon::prelude::*;

// 添加必要的导入
use serde_arrow::schema::TracingOptions;
use crate::dws::model::{BinTestItemIndex, SubBinTestItemIndex};
use crate::model::constant::{ALL, EMPTY, F, P, PF_PASS, SYSTEM};
use crate::model::constant::upload_type::UploadType;
use crate::dws::model::sblot_aggregation::{Sblot, Lot, aggregate_sblot_to_lot};

/// Bin测试项索引服务
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.table.distributed.BinTestItemIndexService
pub struct BinTestItemIndexService {
    test_area: String,
}

impl BinTestItemIndexService {
    /// 创建新的BinTestItemIndexService实例
    pub fn new(test_area: String) -> Self {
        Self { test_area }
    }

    /// 计算Bin测试项索引 - 优化版本
    /// 对应Scala中的calculate方法
    /// 使用Vec优化、并行处理、批处理等优化技术
    pub fn calculate(
        &self,
        test_item_detail: &[Arc<DwsSubTestItemDetail>],
        retest_bin_num_map: Option<&HashMap<Sblot, HashMap<String, i32>>>,
    ) -> Vec<BinTestItemIndex> {
        info!("Calculating bin test item index for {} items", test_item_detail.len());

        // 使用 rayon 并行过滤，优化大数据集处理
        let filter_test_item_detail: Vec<&DwsSubTestItemDetail> = test_item_detail
            .par_iter()
            .filter(|item| {
                item.IS_DUP_FINAL_TEST.unwrap_or(0) == 1 && item.TESTITEM_TYPE != Arc::from(F)
            })
            .map(|item| item.as_ref())
            .collect();

        info!("Filtered {} valid items for bin test item index calculation", filter_test_item_detail.len());

        if DwsService::is_cp_test_area(&self.test_area) {
            self.cp_calculate_optimized(&filter_test_item_detail)
        } else {
            self.ft_calculate_optimized(&filter_test_item_detail, retest_bin_num_map)
        }
    }

    /// 计算bin test item 通用index
    /// 对应Scala中的calculateBinTestItemIndex方法
    pub fn calculate_bin_test_item_index(
        &self,
        items: &[DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> SubBinTestItemIndex {
        // 过滤出首次测试的项目
        let first_bin_test_items: Vec<&DwsSubTestItemDetail> = items
            .iter()
            .filter(|item| item.IS_FIRST_TEST.unwrap_or(0) == 1)
            .collect();

        // 计算最终测试项目
        let final_bin_test_items = self.calculate_final_bin_test_items(items, is_wafer, retest_bin_num_map);

        // 计算首次通过数量
        let first_pass_cnt = first_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() == 1)
            .count() as i64;

        // 计算最终通过数量
        let final_pass_cnt = final_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() == 1)
            .count() as i64;

        // 计算首次失败数量
        let first_fail_cnt = first_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1)
            .count() as i64;

        // 计算最终失败数量
        let final_fail_cnt = final_bin_test_items
            .iter()
            .filter(|item| item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() != 1)
            .count() as i64;

        // 总数量
        let total_cnt = first_bin_test_items.len() as i64;

        // 获取开始和结束时间
        let start_time = items
            .iter()
            .filter_map(|item| item.START_TIME)
            .min();

        let end_time = items
            .iter()
            .filter_map(|item| item.END_TIME)
            .max();

        // 获取首次测试结果值（过滤掉TEST_RESULT >= 2的项目）
        let first_result: Vec<f64> = first_bin_test_items
            .iter()
            .filter(|item| {
                item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() < 2 && item.TEST_VALUE.is_some()
            })
            .map(|item| item.TEST_VALUE.unwrap())
            .collect();

        // 获取最终测试结果值（过滤掉TEST_RESULT >= 2的项目）
        let final_result: Vec<f64> = final_bin_test_items
            .iter()
            .filter(|item| {
                item.TEST_RESULT.is_some() && item.TEST_RESULT.unwrap() < 2 && item.TEST_VALUE.is_some()
            })
            .map(|item| item.TEST_VALUE.unwrap())
            .collect();

        // 计算统计值 - 使用公共工具类方法
        let first_mean = stats::calculate_mean(&first_result);
        let first_sum = stats::calculate_sum(&first_result);
        let first_standard_square_sum = stats::calculate_square_sum(&first_result)
            .map(|sum| sum.sqrt());

        let final_mean = stats::calculate_mean(&final_result);
        let final_sum = stats::calculate_sum(&final_result);
        let final_standard_square_sum = stats::calculate_square_sum(&final_result)
            .map(|sum| sum.sqrt());

        // 计算CP和CPK值，使用安全的计算函数
        let first_item = items.first();
        let (first_cp, first_cpk) = if let Some(item) = first_item {
            let first_std_dev = stats::calculate_std_dev(&first_result);
            let lo_limit_opt = item.LO_LIMIT;
            let hi_limit_opt = item.HI_LIMIT;

            let cp = stats::calculate_cp_safe(first_std_dev, lo_limit_opt, hi_limit_opt);
            let cpk = stats::calculate_cpk_safe(first_mean, first_std_dev, lo_limit_opt, hi_limit_opt);
            (cp, cpk)
        } else {
            (None, None)
        };

        let (final_cp, final_cpk) = if let Some(item) = first_item {
            let final_std_dev = stats::calculate_std_dev(&final_result);
            let lo_limit_opt = item.LO_LIMIT;
            let hi_limit_opt = item.HI_LIMIT;

            let cp = stats::calculate_cp_safe(final_std_dev, lo_limit_opt, hi_limit_opt);
            let cpk = stats::calculate_cpk_safe(final_mean, final_std_dev, lo_limit_opt, hi_limit_opt);
            (cp, cpk)
        } else {
            (None, None)
        };

        // 计算时间键值 - 使用公共工具类方法
        let start_hour_key = start_time
            .map(|st| date::get_day_hour(DateTime::from_timestamp_millis(st).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let start_day_key = start_time
            .map(|st| date::get_day(DateTime::from_timestamp_millis(st).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let end_hour_key = end_time
            .map(|et| date::get_day_hour(DateTime::from_timestamp_millis(et).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        let end_day_key = end_time
            .map(|et| date::get_day(DateTime::from_timestamp_millis(et).unwrap_or_default()))
            .unwrap_or_else(|| EMPTY.to_string());

        // 收集并去重各种名称
        let tester_names = DwsService::mk_string_distinct(&items.iter().map(|item| item.TESTER_NAME.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let tester_types = DwsService::mk_string_distinct(&items.iter().map(|item| item.TESTER_TYPE.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let prober_handler_ids = DwsService::mk_string_distinct(&items.iter().map(|item| item.PROBER_HANDLER_ID.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());
        let prober_card_load_board_ids = DwsService::mk_string_distinct(&items.iter().map(|item| item.PROBECARD_LOADBOARD_ID.to_string()).collect::<Vec<_>>().iter().collect::<Vec<_>>());

        SubBinTestItemIndex {
            tester_names,
            tester_types,
            prober_handler_ids,
            prober_card_load_board_ids,
            start_time: start_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            end_time: end_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            start_hour_key,
            start_day_key,
            end_hour_key,
            end_day_key,
            first_pass_cnt,
            final_pass_cnt,
            first_fail_cnt,
            final_fail_cnt,
            total_cnt,
            first_mean,
            final_mean,
            first_sum,
            final_sum,
            first_standard_square_sum,
            final_standard_square_sum,
        }
    }

    /// CP测试区域的计算逻辑
    fn cp_calculate(&self, test_item_detail: &[&DwsSubTestItemDetail]) -> Vec<BinTestItemIndex> {
        let mut result = Vec::new();

        // 按照完整的BinTestItem key分组，对应Scala中的BinTestItem(Wafer(Lot(...), WAFER_ID), HBIN_PF, HBIN_NUM, HBIN_NAM, SBIN_PF, SBIN_NUM, SBIN_NAM, TEST_ITEM)
        let mut wafer_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            // 构建完整的分组key，包含Lot信息、Wafer信息和Bin信息
            let wafer_key = format!(
                "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                item.CUSTOMER,           // Lot.customer
                item.FACTORY,            // Lot.factory
                item.DEVICE_ID,          // Lot.deviceId
                item.TEST_AREA,          // Lot.testArea
                item.LOT_TYPE,           // Lot.lotType
                item.LOT_ID,             // Lot.lotId
                item.TEST_STAGE,         // Lot.testStage
                item.TEST_PROGRAM,       // Lot.testProgram
                item.WAFER_ID,           // Wafer.waferId
                item.HBIN_PF,            // BinTestItem.hbinPf
                item.HBIN_NUM.unwrap_or(0),  // BinTestItem.hbinNum
                item.HBIN_NAM,           // BinTestItem.hbinNam
                item.SBIN_PF,            // BinTestItem.sbinPf
                item.SBIN_NUM.unwrap_or(0),  // BinTestItem.sbinNum
                item.SBIN_NAM,           // BinTestItem.sbinNam
                item.TEST_ITEM           // BinTestItem.testItem
            );
            wafer_groups.entry(wafer_key).or_insert_with(Vec::new).push(item);
        }

        for (_, items) in wafer_groups {
            if !items.is_empty() {
                // 转换为Vec<DwsSubTestItemDetail>以便调用calculate_bin_test_item_index
                let items_owned: Vec<DwsSubTestItemDetail> = items.iter().map(|&item| item.clone()).collect();

                // 调用通用的calculate_bin_test_item_index方法
                let sub_index = self.calculate_bin_test_item_index(&items_owned, true, None);

                let now = chrono::Utc::now().timestamp_millis();
                let create_hour_key = date::get_day_hour(chrono::Utc::now());
                let create_day_key = date::get_day_hour(chrono::Utc::now());


                // 构建BinTestItemIndex
                let first_item = items[0];

                // 计算file_id
                let upload_type = UploadType::new(&first_item.UPLOAD_TYPE);
                let file_id = if upload_type == UploadType::AUTO {
                    0
                } else {
                    first_item.FILE_ID.unwrap_or(0)
                };

                let file_name = if upload_type == UploadType::AUTO {
                    EMPTY.to_string()
                } else {
                    first_item.FILE_NAME.to_string()
                };

                let bin_test_item_index = BinTestItemIndex {
                    CUSTOMER: first_item.CUSTOMER.to_string(),
                    SUB_CUSTOMER: first_item.SUB_CUSTOMER.to_string(),
                    UPLOAD_TYPE: first_item.UPLOAD_TYPE.to_string(),
                    FILE_ID: file_id,
                    FILE_NAME: file_name,
                    FACTORY: first_item.FACTORY.to_string(),
                    FACTORY_SITE: first_item.FACTORY_SITE.to_string(),
                    FAB: first_item.FAB.to_string(),
                    FAB_SITE: first_item.FAB_SITE.to_string(),
                    TEST_AREA: first_item.TEST_AREA.to_string(),
                    TEST_STAGE: first_item.TEST_STAGE.to_string(),
                    LOT_TYPE: first_item.LOT_TYPE.to_string(),
                    DEVICE_ID: first_item.DEVICE_ID.to_string(),
                    LOT_ID: first_item.LOT_ID.to_string(),
                    WAFER_ID: first_item.WAFER_ID.to_string(),
                    WAFER_ID_KEY: first_item.WAFER_ID.to_string(),
                    WAFER_NO: first_item.WAFER_NO.to_string(),
                    WAFER_NO_KEY: first_item.WAFER_NO.to_string(),
                    TEST_PROGRAM: first_item.TEST_PROGRAM.to_string(),
                    TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.to_string(),
                    TESTER_NAME: sub_index.tester_names,
                    TESTER_TYPE: sub_index.tester_types,
                    PROBER_HANDLER_ID: sub_index.prober_handler_ids,
                    PROBECARD_LOADBOARD_ID: sub_index.prober_card_load_board_ids,
                    START_TIME: sub_index.start_time.map(|t| t.timestamp_millis()),
                    END_TIME: sub_index.end_time.map(|t| t.timestamp_millis()),
                    START_HOUR_KEY: sub_index.start_hour_key,
                    START_DAY_KEY: sub_index.start_day_key,
                    END_HOUR_KEY: sub_index.end_hour_key,
                    END_DAY_KEY: sub_index.end_day_key,
                    HBIN_NUM: first_item.HBIN_NUM,
                    HBIN_NUM_KEY: first_item.HBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_NUM: first_item.SBIN_NUM,
                    SBIN_NUM_KEY: first_item.SBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_PF: first_item.SBIN_PF.to_string(),
                    SBIN_NAM: first_item.SBIN_NAM.to_string(),
                    HBIN_PF: first_item.HBIN_PF.to_string(),
                    HBIN_NAM: first_item.HBIN_NAM.to_string(),
                    HBIN: first_item.HBIN.to_string(),
                    SBIN: first_item.SBIN.to_string(),
                    TESTITEM_TYPE: first_item.TESTITEM_TYPE.to_string(),
                    TEST_NUM: first_item.TEST_NUM,
                    TEST_TXT: first_item.TEST_TXT.to_string(),
                    TEST_ITEM: first_item.TEST_ITEM.to_string(),
                    HI_SPEC: first_item.HI_SPEC,
                    LO_SPEC: first_item.LO_SPEC,
                    HI_LIMIT: first_item.HI_LIMIT,
                    LO_LIMIT: first_item.LO_LIMIT,
                    FIRST_PASS_CNT: Some(sub_index.first_pass_cnt),
                    FINAL_PASS_CNT: Some(sub_index.final_pass_cnt),
                    FIRST_FAIL_CNT: Some(sub_index.first_fail_cnt),
                    FINAL_FAIL_CNT: Some(sub_index.final_fail_cnt),
                    TOTAL_CNT: Some(sub_index.total_cnt),
                    FIRST_MEAN: sub_index.first_mean.map(|v| v as f64),
                    FINAL_MEAN: sub_index.final_mean.map(|v| v as f64),
                    FIRST_SUM: sub_index.first_sum.map(|v| v as f64),
                    FINAL_SUM: sub_index.final_sum.map(|v| v as f64),
                    FIRST_STANDARD_SQUARE_SUM: sub_index.first_standard_square_sum,
                    FINAL_STANDARD_SQUARE_SUM: sub_index.final_standard_square_sum,
                    CREATE_HOUR_KEY: create_hour_key,
                    CREATE_DAY_KEY: create_day_key,
                    CREATE_TIME: now,
                    CREATE_USER: SYSTEM.to_string(),
                    VERSION: 1,
                    PROCESS: first_item.PROCESS.to_string(),
                    UPLOAD_TIME: first_item.UPLOAD_TIME.unwrap_or(0),
                };

                result.push(bin_test_item_index);
            }
        }

        result
    }

    /// FT测试区域的计算逻辑
    fn ft_calculate(
        &self,
        test_item_detail: &[&DwsSubTestItemDetail],
        sblot_retest_bin_num_map: Option<&HashMap<Sblot, HashMap<String, i32>>>,
    ) -> Vec<BinTestItemIndex> {
        let mut result = Vec::new();

        // 将sblot维度聚合成lot维度
        // 对应Scala中的: sblotRetestBinNumMap.groupBy { case (sblot, _) => Lot(...) }.mapValues(...)
        let lot_retest_bin_num_map: HashMap<Lot, HashMap<String, i32>> = if let Some(sblot_map) = sblot_retest_bin_num_map {
            aggregate_sblot_to_lot(sblot_map.clone())
        } else {
            HashMap::new()
        };

        // 按照完整的BinTestItem key分组，对应Scala中的BinTestItem(Wafer(Lot(...), EMPTY), HBIN_PF, HBIN_NUM, HBIN_NAM, SBIN_PF, SBIN_NUM, SBIN_NAM, TEST_ITEM)
        let mut lot_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            // 构建完整的分组key，包含Lot信息、空的Wafer信息和Bin信息
            let lot_key = format!(
                "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                item.CUSTOMER,           // Lot.customer
                item.FACTORY,            // Lot.factory
                item.DEVICE_ID,          // Lot.deviceId
                item.TEST_AREA,          // Lot.testArea
                item.LOT_TYPE,           // Lot.lotType
                item.LOT_ID,             // Lot.lotId
                item.TEST_STAGE,         // Lot.testStage
                item.TEST_PROGRAM,       // Lot.testProgram
                EMPTY,                   // EMPTY for Wafer.waferId (FT测试没有wafer信息)
                item.HBIN_PF,            // BinTestItem.hbinPf
                item.HBIN_NUM.unwrap_or(0),  // BinTestItem.hbinNum
                item.HBIN_NAM,           // BinTestItem.hbinNam
                item.SBIN_PF,            // BinTestItem.sbinPf
                item.SBIN_NUM.unwrap_or(0),  // BinTestItem.sbinNum
                item.SBIN_NAM,           // BinTestItem.sbinNam
                item.TEST_ITEM           // BinTestItem.testItem
            );
            lot_groups.entry(lot_key).or_insert_with(Vec::new).push(item);
        }

        for (_, items) in lot_groups {
            if !items.is_empty() {
                // 转换为Vec<DwsSubTestItemDetail>以便调用calculate_bin_test_item_index
                let items_owned: Vec<DwsSubTestItemDetail> = items.iter().map(|&item| item.clone()).collect();

                // 为FT测试准备retest_bin_num_map，使用聚合后的lot维度数据
                let first_item = items[0];
                let lot = Lot::from_test_item_detail(first_item);
                let current_retest_map = lot_retest_bin_num_map.get(&lot);

                // 调用通用的calculate_bin_test_item_index方法
                let sub_index = self.calculate_bin_test_item_index(&items_owned, false, current_retest_map);

                // 构建BinTestItemIndex
                let first_item = items[0];
                let now = chrono::Utc::now().timestamp_millis();
                let create_hour_key = date::get_day_hour(chrono::Utc::now());
                let create_day_key = date::get_day_hour(chrono::Utc::now());

                // 计算file_id
                let upload_type = UploadType::new(&first_item.UPLOAD_TYPE);
                let file_id = if upload_type == UploadType::AUTO {
                    0
                } else {
                    first_item.FILE_ID.unwrap_or(0)
                };

                let file_name = if upload_type == UploadType::AUTO {
                    EMPTY.to_string()
                } else {
                    first_item.FILE_NAME.to_string()
                };

                let bin_test_item_index = BinTestItemIndex {
                    CUSTOMER: first_item.CUSTOMER.to_string(),
                    SUB_CUSTOMER: first_item.SUB_CUSTOMER.to_string(),
                    UPLOAD_TYPE: first_item.UPLOAD_TYPE.to_string(),
                    FILE_ID: file_id,
                    FILE_NAME: file_name,
                    FACTORY: first_item.FACTORY.to_string(),
                    FACTORY_SITE: first_item.FACTORY_SITE.to_string(),
                    FAB: first_item.FAB.to_string(),
                    FAB_SITE: first_item.FAB_SITE.to_string(),
                    TEST_AREA: first_item.TEST_AREA.to_string(),
                    TEST_STAGE: first_item.TEST_STAGE.to_string(),
                    LOT_TYPE: first_item.LOT_TYPE.to_string(),
                    DEVICE_ID: first_item.DEVICE_ID.to_string(),
                    LOT_ID: first_item.LOT_ID.to_string(),
                    WAFER_ID: EMPTY.to_string(), // FT测试没有wafer信息
                    WAFER_ID_KEY: EMPTY.to_string(),
                    WAFER_NO: EMPTY.to_string(),
                    WAFER_NO_KEY: EMPTY.to_string(),
                    TEST_PROGRAM: first_item.TEST_PROGRAM.to_string(),
                    TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.to_string(),
                    TESTER_NAME: sub_index.tester_names,
                    TESTER_TYPE: sub_index.tester_types,
                    PROBER_HANDLER_ID: sub_index.prober_handler_ids,
                    PROBECARD_LOADBOARD_ID: sub_index.prober_card_load_board_ids,
                    START_TIME: sub_index.start_time.map(|t| t.timestamp_millis()),
                    END_TIME: sub_index.end_time.map(|t| t.timestamp_millis()),
                    START_HOUR_KEY: sub_index.start_hour_key,
                    START_DAY_KEY: sub_index.start_day_key,
                    END_HOUR_KEY: sub_index.end_hour_key,
                    END_DAY_KEY: sub_index.end_day_key,
                    HBIN_NUM: first_item.HBIN_NUM,
                    HBIN_NUM_KEY: first_item.HBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_NUM: first_item.SBIN_NUM,
                    SBIN_NUM_KEY: first_item.SBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
                    SBIN_PF: first_item.SBIN_PF.to_string(),
                    SBIN_NAM: first_item.SBIN_NAM.to_string(),
                    HBIN_PF: first_item.HBIN_PF.to_string(),
                    HBIN_NAM: first_item.HBIN_NAM.to_string(),
                    HBIN: first_item.HBIN.to_string(),
                    SBIN: first_item.SBIN.to_string(),
                    TESTITEM_TYPE: first_item.TESTITEM_TYPE.to_string(),
                    TEST_NUM: first_item.TEST_NUM,
                    TEST_TXT: first_item.TEST_TXT.to_string(),
                    TEST_ITEM: first_item.TEST_ITEM.to_string(),
                    HI_SPEC: first_item.HI_SPEC,
                    LO_SPEC: first_item.LO_SPEC,
                    HI_LIMIT: first_item.HI_LIMIT,
                    LO_LIMIT: first_item.LO_LIMIT,
                    FIRST_PASS_CNT: Some(sub_index.first_pass_cnt),
                    FINAL_PASS_CNT: Some(sub_index.final_pass_cnt),
                    FIRST_FAIL_CNT: Some(sub_index.first_fail_cnt),
                    FINAL_FAIL_CNT: Some(sub_index.final_fail_cnt),
                    TOTAL_CNT: Some(sub_index.total_cnt),
                    FIRST_MEAN: sub_index.first_mean,
                    FINAL_MEAN: sub_index.final_mean,
                    FIRST_SUM: sub_index.first_sum,
                    FINAL_SUM: sub_index.final_sum,
                    FIRST_STANDARD_SQUARE_SUM: sub_index.first_standard_square_sum,
                    FINAL_STANDARD_SQUARE_SUM: sub_index.final_standard_square_sum,
                    CREATE_HOUR_KEY: create_hour_key,
                    CREATE_DAY_KEY: create_day_key,
                    CREATE_TIME: now,
                    CREATE_USER: SYSTEM.to_string(),
                    VERSION: 1,
                    PROCESS: first_item.PROCESS.to_string(),
                    UPLOAD_TIME: first_item.UPLOAD_TIME.unwrap_or(0),
                };

                result.push(bin_test_item_index);
            }
        }

        result
    }

    /// 计算final bin,FT需要考虑RETEST_BIN_NUM
    /// 对应Scala中的calculateFinalBinTestItems方法
    fn calculate_final_bin_test_items<'a>(
        &self,
        items: &'a [DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> Vec<&'a DwsSubTestItemDetail> {
        if items.is_empty() {
            return Vec::new();
        }

        let head = &items[0];

        if is_wafer {
            // CP
            if head.HBIN_PF == PF_PASS.into() { // PF_PASS
                items.iter().collect()
            } else {
                items
                    .iter()
                    .filter(|item| item.IS_FINAL_TEST.unwrap_or(0) == 1)
                    .collect()
            }
        } else {
            // FT复测过的TEST_ITEM
            let current_bin_retest: HashMap<String, i32> = if let Some(retest_map) = retest_bin_num_map {
                retest_map
                    .iter()
                    .filter(|(key, _)| {
                        key.is_empty() ||
                        *key == ALL ||
                        key.split(',').any(|k| k == head.HBIN_NUM.unwrap_or(0).to_string())
                    })
                    .map(|(k, v)| (k.clone(), *v))
                    .collect()
            } else {
                HashMap::new()
            };

            if head.HBIN_PF == PF_PASS.into() || current_bin_retest.is_empty() { // PF_PASS
                items.iter().collect()
            } else {
                let bin_max_offline_retest = current_bin_retest.values().max().unwrap_or(&0);
                items
                    .iter()
                    .filter(|item| item.OFFLINE_RETEST.unwrap_or(0) >= *bin_max_offline_retest)
                    .collect()
            }
        }
    }

    /// 计算bin test item 通用index - 优化版本
    /// 使用并行处理和内存优化技术
    pub fn calculate_bin_test_item_index_optimized(
        &self,
        items: &[&DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> SubBinTestItemIndex {
        if items.is_empty() {
            return SubBinTestItemIndex::default();
        }

        // 使用并行处理过滤首次测试项目
        let first_bin_test_items: Vec<&DwsSubTestItemDetail> = items
            .par_iter()
            .filter(|item| item.IS_FIRST_TEST.unwrap_or(0) == 1)
            .copied()
            .collect();

        // 并行计算最终测试项目
        let final_bin_test_items = self.calculate_final_bin_test_items_optimized(items, is_wafer, retest_bin_num_map);

        // 并行计算统计值
        let (first_pass_cnt, first_fail_cnt, first_result): (i64, i64, Vec<f64>) = first_bin_test_items
            .par_iter()
            .map(|item| {
                let pass = if item.TEST_RESULT.unwrap_or(0) == 1 { 1 } else { 0 };
                let fail = if item.TEST_RESULT.unwrap_or(0) != 1 { 1 } else { 0 };
                let value = if item.TEST_RESULT.unwrap_or(0) < 2 && item.TEST_VALUE.is_some() {
                    vec![item.TEST_VALUE.unwrap()]
                } else {
                    vec![]
                };
                (pass, fail, value)
            })
            .reduce(
                || (0, 0, Vec::new()),
                |a, b| (a.0 + b.0, a.1 + b.1, [a.2, b.2].concat())
            );

        let (final_pass_cnt, final_fail_cnt, final_result): (i64, i64, Vec<f64>) = final_bin_test_items
            .par_iter()
            .map(|item| {
                let pass = if item.TEST_RESULT.unwrap_or(0) == 1 { 1 } else { 0 };
                let fail = if item.TEST_RESULT.unwrap_or(0) != 1 { 1 } else { 0 };
                let value = if item.TEST_RESULT.unwrap_or(0) < 2 && item.TEST_VALUE.is_some() {
                    vec![item.TEST_VALUE.unwrap()]
                } else {
                    vec![]
                };
                (pass, fail, value)
            })
            .reduce(
                || (0, 0, Vec::new()),
                |a, b| (a.0 + b.0, a.1 + b.1, [a.2, b.2].concat())
            );

        // 并行计算时间范围
        let (start_time, end_time) = items
            .par_iter()
            .map(|item| (item.START_TIME, item.END_TIME))
            .reduce(
                || (None, None),
                |a, b| (
                    match (a.0, b.0) {
                        (Some(a), Some(b)) => Some(a.min(b)),
                        (Some(a), None) => Some(a),
                        (None, Some(b)) => Some(b),
                        _ => None,
                    },
                    match (a.1, b.1) {
                        (Some(a), Some(b)) => Some(a.max(b)),
                        (Some(a), None) => Some(a),
                        (None, Some(b)) => Some(b),
                        _ => None,
                    }
                )
            );

        // 计算统计值
        let first_mean = stats::calculate_mean(&first_result);
        let first_sum = stats::calculate_sum(&first_result);
        let first_standard_square_sum = stats::calculate_square_sum(&first_result).map(|sum| sum.sqrt());

        let final_mean = stats::calculate_mean(&final_result);
        let final_sum = stats::calculate_sum(&final_result);
        let final_standard_square_sum = stats::calculate_square_sum(&final_result).map(|sum| sum.sqrt());

        // 并行收集和去重字段
        let tester_names = DwsService::mk_string_distinct(
            &items.par_iter()
                .map(|item| item.TESTER_NAME.to_string())
                .collect::<Vec<_>>()
                .iter()
                .collect::<Vec<_>>()
        );

        let tester_types = DwsService::mk_string_distinct(
            &items.par_iter()
                .map(|item| item.TESTER_TYPE.to_string())
                .collect::<Vec<_>>()
                .iter()
                .collect::<Vec<_>>()
        );

        SubBinTestItemIndex {
            tester_names,
            tester_types,
            prober_handler_ids: DwsService::mk_string_distinct(
                &items.par_iter()
                    .map(|item| item.PROBER_HANDLER_ID.to_string())
                    .collect::<Vec<_>>()
                    .iter()
                    .collect::<Vec<_>>()
            ),
            prober_card_load_board_ids: DwsService::mk_string_distinct(
                &items.par_iter()
                    .map(|item| item.PROBECARD_LOADBOARD_ID.to_string())
                    .collect::<Vec<_>>()
                    .iter()
                    .collect::<Vec<_>>()
            ),
            start_time: start_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            end_time: end_time.map(|t| DateTime::from_timestamp_millis(t).unwrap_or_default()),
            start_hour_key: start_time
                .map(|st| date::get_day_hour(DateTime::from_timestamp_millis(st).unwrap_or_default()))
                .unwrap_or_else(|| EMPTY.to_string()),
            start_day_key: start_time
                .map(|st| date::get_day(DateTime::from_timestamp_millis(st).unwrap_or_default()))
                .unwrap_or_else(|| EMPTY.to_string()),
            end_hour_key: end_time
                .map(|et| date::get_day_hour(DateTime::from_timestamp_millis(et).unwrap_or_default()))
                .unwrap_or_else(|| EMPTY.to_string()),
            end_day_key: end_time
                .map(|et| date::get_day(DateTime::from_timestamp_millis(et).unwrap_or_default()))
                .unwrap_or_else(|| EMPTY.to_string()),
            first_pass_cnt,
            final_pass_cnt,
            first_fail_cnt,
            final_fail_cnt,
            total_cnt: first_bin_test_items.len() as i64,
            first_mean,
            final_mean,
            first_sum,
            final_sum,
            first_standard_square_sum,
            final_standard_square_sum,
        }
    }

    /// 计算最终bin测试项目 - 优化版本
    fn calculate_final_bin_test_items_optimized<'a>(
        &self,
        items: &'a [&'a DwsSubTestItemDetail],
        is_wafer: bool,
        retest_bin_num_map: Option<&HashMap<String, i32>>,
    ) -> Vec<&'a DwsSubTestItemDetail> {
        if items.is_empty() {
            return Vec::new();
        }

        let head = items[0];

        if is_wafer {
            // CP: 使用并行处理
            if head.HBIN_PF == PF_PASS.into() {
                items.to_vec()
            } else {
                items.par_iter()
                    .filter(|item| item.IS_FINAL_TEST.unwrap_or(0) == 1)
                    .copied()
                    .collect()
            }
        } else {
            // FT: 优化复测处理
            let current_bin_retest: HashMap<String, i32> = if let Some(retest_map) = retest_bin_num_map {
                retest_map.par_iter()
                    .filter(|(key, _)| {
                        key.is_empty() ||
                        *key == ALL ||
                        key.split(',').any(|k| k == head.HBIN_NUM.unwrap_or(0).to_string())
                    })
                    .map(|(k, v)| (k.clone(), *v))
                    .collect()
            } else {
                HashMap::new()
            };

            let retest_bin_num = if let Some(num) = current_bin_retest.get(&head.TEST_ITEM.to_string()) {
                *num
            } else if let Some(num) = current_bin_retest.get("") {
                *num
            } else if let Some(num) = current_bin_retest.get(ALL) {
                *num
            } else {
                1
            };

            // 并行过滤
            items.par_iter()
                .filter(|item| item.ONLINE_RETEST.unwrap_or(0) <= retest_bin_num)
                .copied()
                .collect()
        }
    }

    /// CP测试区域的计算逻辑 - 优化版本
    fn cp_calculate_optimized(&self, test_item_detail: &[&DwsSubTestItemDetail]) -> Vec<BinTestItemIndex> {
        info!("CP calculate optimized for {} items", test_item_detail.len());

        // 使用并行分组
        let wafer_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = test_item_detail
            .par_iter()
            .map(|&item| {
                let wafer_key = format!(
                    "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                    item.CUSTOMER, item.FACTORY, item.DEVICE_ID, item.TEST_AREA,
                    item.LOT_TYPE, item.LOT_ID, item.TEST_STAGE, item.TEST_PROGRAM,
                    item.WAFER_ID, item.HBIN_PF, item.HBIN_NUM.unwrap_or(0),
                    item.HBIN_NAM, item.SBIN_PF, item.SBIN_NUM.unwrap_or(0),
                    item.SBIN_NAM, item.TEST_ITEM
                );
                (wafer_key, item)
            })
            .collect::<Vec<_>>()
            .into_iter()
            .fold(HashMap::new(), |mut acc, (key, item)| {
                acc.entry(key).or_insert_with(Vec::new).push(item);
                acc
            });

        // 并行处理每个分组
        let result: Vec<BinTestItemIndex> = wafer_groups
            .par_iter()
            .filter_map(|(_, items)| {
                if items.is_empty() { return None; }

                let first_item = items[0];
                let sub_index = self.calculate_bin_test_item_index_optimized(items, true, None);

                Some(self.build_bin_test_item_index_from_sub_index(first_item, &sub_index))
            })
            .collect();

        info!("CP calculate optimized completed: {} results", result.len());
        result
    }

    /// FT测试区域的计算逻辑 - 优化版本
    fn ft_calculate_optimized(
        &self,
        test_item_detail: &[&DwsSubTestItemDetail],
        sblot_retest_bin_num_map: Option<&HashMap<Sblot, HashMap<String, i32>>>,
    ) -> Vec<BinTestItemIndex> {
        info!("FT calculate optimized for {} items", test_item_detail.len());

        // 并行聚合sblot到lot维度
        let lot_retest_bin_num_map: HashMap<Lot, HashMap<String, i32>> =
            if let Some(sblot_map) = sblot_retest_bin_num_map {
                aggregate_sblot_to_lot(sblot_map.clone())
            } else {
                HashMap::new()
            };

        // 并行分组处理
        let lot_groups: HashMap<String, Vec<&DwsSubTestItemDetail>> = test_item_detail
            .par_iter()
            .map(|&item| {
                let lot_key = format!(
                    "{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}_{}",
                    item.CUSTOMER, item.FACTORY, item.DEVICE_ID, item.TEST_AREA,
                    item.LOT_TYPE, item.LOT_ID, item.TEST_STAGE, item.TEST_PROGRAM,
                    EMPTY, item.HBIN_PF, item.HBIN_NUM.unwrap_or(0),
                    item.HBIN_NAM, item.SBIN_PF, item.SBIN_NUM.unwrap_or(0),
                    item.SBIN_NAM, item.TEST_ITEM
                );
                (lot_key, item)
            })
            .collect::<Vec<_>>()
            .into_iter()
            .fold(HashMap::new(), |mut acc, (key, item)| {
                acc.entry(key).or_insert_with(Vec::new).push(item);
                acc
            });

        // 并行处理每个分组
        let result: Vec<BinTestItemIndex> = lot_groups
            .par_iter()
            .filter_map(|(_, items)| {
                if items.is_empty() { return None; }

                let first_item = items[0];
                let lot = Lot::from_test_item_detail(first_item);
                let current_retest_map = lot_retest_bin_num_map.get(&lot);

                let sub_index = self.calculate_bin_test_item_index_optimized(items, false, current_retest_map);

                Some(self.build_bin_test_item_index_from_sub_index_ft(first_item, &sub_index))
            })
            .collect();

        info!("FT calculate optimized completed: {} results", result.len());
        result
    }

    /// 从sub_index构建BinTestItemIndex - CP版本
    fn build_bin_test_item_index_from_sub_index(
        &self,
        first_item: &DwsSubTestItemDetail,
        sub_index: &SubBinTestItemIndex,
    ) -> BinTestItemIndex {
        let now = chrono::Utc::now().timestamp_millis();
        let create_hour_key = date::get_day_hour(chrono::Utc::now());
        let create_day_key = date::get_day_hour(chrono::Utc::now());

        let upload_type = UploadType::new(&first_item.UPLOAD_TYPE);
        let (file_id, file_name) = if upload_type == UploadType::AUTO {
            (0, EMPTY.to_string())
        } else {
            (first_item.FILE_ID.unwrap_or(0), first_item.FILE_NAME.to_string())
        };

        BinTestItemIndex {
            CUSTOMER: first_item.CUSTOMER.to_string(),
            SUB_CUSTOMER: first_item.SUB_CUSTOMER.to_string(),
            UPLOAD_TYPE: first_item.UPLOAD_TYPE.to_string(),
            FILE_ID: file_id,
            FILE_NAME: file_name,
            FACTORY: first_item.FACTORY.to_string(),
            FACTORY_SITE: first_item.FACTORY_SITE.to_string(),
            FAB: first_item.FAB.to_string(),
            FAB_SITE: first_item.FAB_SITE.to_string(),
            TEST_AREA: first_item.TEST_AREA.to_string(),
            TEST_STAGE: first_item.TEST_STAGE.to_string(),
            LOT_TYPE: first_item.LOT_TYPE.to_string(),
            DEVICE_ID: first_item.DEVICE_ID.to_string(),
            LOT_ID: first_item.LOT_ID.to_string(),
            WAFER_ID: first_item.WAFER_ID.to_string(),
            WAFER_ID_KEY: first_item.WAFER_ID.to_string(),
            WAFER_NO: first_item.WAFER_NO.to_string(),
            WAFER_NO_KEY: first_item.WAFER_NO.to_string(),
            TEST_PROGRAM: first_item.TEST_PROGRAM.to_string(),
            TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.to_string(),
            TESTER_NAME: sub_index.tester_names.clone(),
            TESTER_TYPE: sub_index.tester_types.clone(),
            PROBER_HANDLER_ID: sub_index.prober_handler_ids.clone(),
            PROBECARD_LOADBOARD_ID: sub_index.prober_card_load_board_ids.clone(),
            START_TIME: sub_index.start_time.map(|t| t.timestamp_millis()),
            END_TIME: sub_index.end_time.map(|t| t.timestamp_millis()),
            START_HOUR_KEY: sub_index.start_hour_key.clone(),
            START_DAY_KEY: sub_index.start_day_key.clone(),
            END_HOUR_KEY: sub_index.end_hour_key.clone(),
            END_DAY_KEY: sub_index.end_day_key.clone(),
            HBIN_NUM: first_item.HBIN_NUM,
            HBIN_NUM_KEY: first_item.HBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
            SBIN_NUM: first_item.SBIN_NUM,
            SBIN_NUM_KEY: first_item.SBIN_NUM.map(|n| n.to_string()).unwrap_or_default(),
            SBIN_PF: first_item.SBIN_PF.to_string(),
            SBIN_NAM: first_item.SBIN_NAM.to_string(),
            HBIN_PF: first_item.HBIN_PF.to_string(),
            HBIN_NAM: first_item.HBIN_NAM.to_string(),
            HBIN: first_item.HBIN.to_string(),
            SBIN: first_item.SBIN.to_string(),
            TESTITEM_TYPE: first_item.TESTITEM_TYPE.to_string(),
            TEST_NUM: first_item.TEST_NUM,
            TEST_TXT: first_item.TEST_TXT.to_string(),
            TEST_ITEM: first_item.TEST_ITEM.to_string(),
            HI_SPEC: first_item.HI_SPEC,
            LO_SPEC: first_item.LO_SPEC,
            HI_LIMIT: first_item.HI_LIMIT,
            LO_LIMIT: first_item.LO_LIMIT,
            FIRST_PASS_CNT: Some(sub_index.first_pass_cnt),
            FINAL_PASS_CNT: Some(sub_index.final_pass_cnt),
            FIRST_FAIL_CNT: Some(sub_index.first_fail_cnt),
            FINAL_FAIL_CNT: Some(sub_index.final_fail_cnt),
            TOTAL_CNT: Some(sub_index.total_cnt),
            FIRST_MEAN: sub_index.first_mean.map(|v| v as f64),
            FINAL_MEAN: sub_index.final_mean.map(|v| v as f64),
            FIRST_SUM: sub_index.first_sum.map(|v| v as f64),
            FINAL_SUM: sub_index.final_sum.map(|v| v as f64),
            FIRST_STANDARD_SQUARE_SUM: sub_index.first_standard_square_sum,
            FINAL_STANDARD_SQUARE_SUM: sub_index.final_standard_square_sum,
            CREATE_HOUR_KEY: create_hour_key,
            CREATE_DAY_KEY: create_day_key,
            CREATE_TIME: now,
            CREATE_USER: SYSTEM.to_string(),
            VERSION: 1,
            PROCESS: first_item.PROCESS.to_string(),
            UPLOAD_TIME: first_item.UPLOAD_TIME.unwrap_or(0),
        }
    }

    /// 从sub_index构建BinTestItemIndex - FT版本
    fn build_bin_test_item_index_from_sub_index_ft(
        &self,
        first_item: &DwsSubTestItemDetail,
        sub_index: &SubBinTestItemIndex,
    ) -> BinTestItemIndex {
        let mut result = self.build_bin_test_item_index_from_sub_index(first_item, sub_index);
        // FT特殊处理：清空wafer相关字段
        result.WAFER_ID = EMPTY.to_string();
        result.WAFER_ID_KEY = EMPTY.to_string();
        result.WAFER_NO = EMPTY.to_string();
        result.WAFER_NO_KEY = EMPTY.to_string();
        result
    }

}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_bin_test_item_index() {
        use chrono::Utc;

        let service = BinTestItemIndexService::new("CP".to_string());

        // 创建测试数据
        let test_items = vec![
            DwsSubTestItemDetail {
                IS_FIRST_TEST: Some(1),
                TEST_RESULT: Some(1),
                TEST_VALUE: Some(1.5),
                TESTER_NAME: Arc::from("TESTER1"),
                TESTER_TYPE: Arc::from("TYPE1"),
                PROBER_HANDLER_ID: Arc::from("HANDLER1"),
                PROBECARD_LOADBOARD_ID: Arc::from("LOADBOARD1"),
                START_TIME: None,
                END_TIME: None,
                HBIN_PF: Arc::from("P"),
                IS_FINAL_TEST: Some(1),
                OFFLINE_RETEST: Some(0),
                ..Default::default()
            },
            DwsSubTestItemDetail {
                IS_FIRST_TEST: Some(1),
                TEST_RESULT: Some(0),
                TEST_VALUE: Some(2.5),
                TESTER_NAME: Arc::from("TESTER2"),
                TESTER_TYPE: Arc::from("TYPE2"),
                PROBER_HANDLER_ID: Arc::from("HANDLER2"),
                PROBECARD_LOADBOARD_ID: Arc::from("LOADBOARD2"),
                START_TIME: None,
                END_TIME: None,
                HBIN_PF: Arc::from("F"),
                IS_FINAL_TEST: Some(1),
                OFFLINE_RETEST: Some(0),
                ..Default::default()
            },
        ];

        let result = service.calculate_bin_test_item_index(&test_items, true, None);

        assert_eq!(result.first_pass_cnt, 1);
        assert_eq!(result.first_fail_cnt, 1);
        assert_eq!(result.total_cnt, 2);
        assert!(result.first_mean.is_some());
        assert_eq!(result.first_mean.unwrap(), 2.0); // (1.5 + 2.5) / 2
    }

    #[test]
    fn test_mk_string_distinct() {
        let test1 = "test1".to_string();
        let test2 = "test2".to_string();
        let test3 = "test3".to_string();
        let empty = "".to_string();

        let strings = vec![
            &test1,
            &test2,
            &test1,
            &empty,
            &test3,
        ];

        let result = DwsService::mk_string_distinct(&strings);
        assert_eq!(result, "test1,test2,test3");
    }
}

// 为DwsSubTestItemDetail实现Default trait以便测试
impl Default for DwsSubTestItemDetail {
    fn default() -> Self {
        Self {
            CUSTOMER: String::new().into(),
            SUB_CUSTOMER: String::new().into(),
            UPLOAD_TYPE: String::new().into(),
            FILE_ID: None,
            FILE_NAME: String::new().into(),
            FILE_TYPE: String::new().into(),
            FACTORY: String::new().into(),
            FACTORY_SITE: String::new().into(),
            FAB: String::new().into(),
            FAB_SITE: String::new().into(),
            LOT_TYPE: String::new().into(),
            TEST_AREA: String::new().into(),
            ECID: String::new().into(),
            OFFLINE_RETEST: None,
            ONLINE_RETEST: None,
            INTERRUPT: None,
            DUP_RETEST: None,
            BATCH_NUM: None,
            IS_FIRST_TEST: None,
            IS_FINAL_TEST: None,
            IS_FIRST_TEST_IGNORE_TP: None,
            IS_FINAL_TEST_IGNORE_TP: None,
            IS_DUP_FIRST_TEST: None,
            IS_DUP_FINAL_TEST: None,
            IS_DUP_FIRST_TEST_IGNORE_TP: None,
            IS_DUP_FINAL_TEST_IGNORE_TP: None,
            WAFER_ID: String::new().into(),
            WAFER_NO: String::new().into(),
            LOT_ID: String::new().into(),
            SBLOT_ID: String::new().into(),
            WAFER_LOT_ID: Arc::from(""),
            PROBER_HANDLER_ID: String::new().into(),
            TESTER_TYPE: String::new().into(),
            TEST_STAGE: String::new().into(),
            DEVICE_ID: String::new().into(),
            TEST_PROGRAM: String::new().into(),
            TEST_TEMPERATURE: String::new().into(),
            TEST_PROGRAM_VERSION: String::new().into(),
            TESTER_NAME: String::new().into(),
            PROBECARD_LOADBOARD_ID: String::new().into(),
            SITE: None,
            X_COORD: None,
            Y_COORD: None,
            PART_ID: String::new().into(),
            SBIN_NUM: None,
            SBIN_PF: String::new().into(),
            SBIN_NAM: String::new().into(),
            HBIN_NUM: None,
            HBIN_PF: String::new().into(),
            HBIN_NAM: String::new().into(),
            HBIN: String::new().into(),
            SBIN: String::new().into(),
            TEST_NUM: None,
            TEST_TXT: String::new().into(),
            TEST_ITEM: String::new().into(),
            TEST_ORDER: None,
            LO_SPEC: None,
            HI_SPEC: None,
            LO_LIMIT: None,
            HI_LIMIT: None,
            ORIGIN_HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            TEST_VALUE: None,
            TEST_RESULT: None,
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: String::new().into(),
            START_DAY_KEY: String::new().into(),
            END_HOUR_KEY: String::new().into(),
            END_DAY_KEY: String::new().into(),
            FLOW_ID: String::new().into(),
            UNITS: String::new().into(),
            ORIGIN_UNITS: String::new().into(),
            TESTITEM_TYPE: String::new().into(),
            C_PART_ID: None,
            RETEST_BIN_NUM: String::new().into(),
            PROCESS: String::new().into(),
            CREATE_TIME: Some(0),
            UPLOAD_TIME: Some(0),
        }
    }
}
