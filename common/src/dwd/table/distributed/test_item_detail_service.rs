use std::collections::HashMap;
use std::error::Error;

use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dto::ods::test_item_data_parquet::TestItemDataParquet;
use crate::dwd::model::value::die_test_info::DieTestInfo;
use crate::dwd::table::test_item_detail_common_service::TestItemDetailCommonService;
use crate::model::constant::test_state::TestState;
use crate::model::key::die_key::Die<PERSON>ey;

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/table/distributed/TestItemDetailService.scala

/// TestItemDetailService handles distributed processing of test item details
/// Extends TestItemDetailCommonService with distributed-specific functionality
#[derive(Debug, Clone)]
pub struct TestItemDetailService {
    test_area: String,
    common_service: TestItemDetailCommonService,
}

impl TestItemDetailService {
    /// Create new TestItemDetailService with test area
    ///
    /// Corresponds to: TestItemDetailService.scala:30 (constructor)
    pub fn new(test_area: String) -> Self {
        Self { test_area, common_service: TestItemDetailCommonService::new() }
    }

    /// Calculate CP test item details
    ///
    /// Corresponds to: TestItemDetailService.scala:43-62
    /// def calculateCpTestItemDetail(spark: SparkSession, sourceTestItemData: Dataset[TestItemData],
    ///                              fileCategory: String, dieTestInfoMap: Broadcast[Map[DieKey, DieTestInfo]],
    ///                              needMultiplyScale: Boolean, standardUnits: String, needClearInvalidData: Boolean,
    ///                              testNumForceZeroTestProgramList: Broadcast[List[String]], fileUseBinDefinitionMap: Broadcast[Map[Long, Integer]]): Dataset[SubTestItemDetail]
    pub fn calculate_cp_test_item_detail(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
        test_num_force_zero_test_program_list: &[String],
        file_use_bin_definition_map: &HashMap<i64, i32>,
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        // Create standard units map (corresponds to line 52 in Scala)
        let standard_units_map = self.common_service.get_standard_units(need_multiply_scale, standard_units)?;

        // Filter valid file data - keep only items where DieKey exists in dieTestInfoMap (corresponds to line 55 in Scala)
        let result = source_test_item_data
            .iter()
            .filter(|item| {
                let die_key = DieKey::new(item.fileId.unwrap_or(0), item.cPartId.unwrap_or(0) as i64);
                die_test_info_map.contains_key(&die_key)
            })
            .map(|item| {
                // Get use_bin_definition flag from fileUseBinDefinitionMap (corresponds to Scala broadcast variable usage)
                let use_bin_definition = file_use_bin_definition_map
                    .get(&item.fileId.unwrap_or(0))
                    .copied()
                    .unwrap_or(0);
                self.common_service
                    .build_cp_test_item_detail(item, &standard_units_map, file_category, need_multiply_scale, use_bin_definition)
                    .unwrap()
            })
            .filter(|sub_test_item_detail| {
                // Filter invalid data if needClearInvalidData is true (corresponds to line 59 in Scala)
                if need_clear_invalid_data {
                    sub_test_item_detail.TEST_STATE == Some(TestState::Valid.to_string())
                } else {
                    true
                }
            })
            .map(|sub_test_item_detail| {
                self.common_service.fill_die_test_info(
                    sub_test_item_detail,
                    die_test_info_map,
                    test_num_force_zero_test_program_list,
                )
            })
            .collect::<Result<Vec<SubTestItemDetail>, Box<dyn Error>>>()?;

        Ok(result)
    }

    /// Calculate FT test item details
    ///
    /// Corresponds to: TestItemDetailService.scala:84-103
    /// def calculateFtTestItemDetail(spark: SparkSession, sourceTestItemData: Dataset[TestItemData],
    ///                              fileCategory: String, dieTestInfoMap: Broadcast[Map[DieKey, DieTestInfo]],
    ///                              needMultiplyScale: Boolean, standardUnits: String, needClearInvalidData: Boolean,
    ///                              fileUseBinDefinitionMap: Broadcast[Map[Long, Integer]]): Dataset[SubTestItemDetail]
    pub fn calculate_ft_test_item_detail(
        &self,
        source_test_item_data: Vec<TestItemDataParquet>,
        file_category: &str,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        need_multiply_scale: bool,
        standard_units: &str,
        need_clear_invalid_data: bool,
        file_use_bin_definition_map: &HashMap<i64, i32>,
    ) -> Result<Vec<SubTestItemDetail>, Box<dyn Error>> {
        let standard_units_map = self.common_service.get_standard_units(need_multiply_scale, standard_units)?;

        let result = source_test_item_data
            .iter()
            // 去重
            .filter(|item| {
                let die_key = DieKey::new(item.fileId.unwrap_or(0), item.cPartId.unwrap_or(0) as i64);
                die_test_info_map.contains_key(&die_key)
            })
            // 构建TestItemDetail
            .map(|item| {
                // Get use_bin_definition flag from fileUseBinDefinitionMap (corresponds to Scala broadcast variable usage)
                let use_bin_definition = file_use_bin_definition_map
                    .get(&item.fileId.unwrap_or(0))
                    .copied()
                    .unwrap_or(0);
                self.common_service
                    .build_ft_test_item_detail(item, &standard_units_map, file_category, need_multiply_scale, use_bin_definition)
                    .unwrap()
            })
            .filter(|sub_test_item_detail| {
                if need_clear_invalid_data {
                    sub_test_item_detail.TEST_STATE == Some(TestState::Valid.to_string())
                } else {
                    true
                }
            })
            .collect::<Vec<SubTestItemDetail>>();
        Ok(result)
    }

    /// Fill die test info for a SubTestItemDetail
    ///
    /// Corresponds to: TestItemDetailService.scala:fillDieTestInfo method usage
    pub fn fill_die_test_info(
        &self,
        sub_test_item_detail: SubTestItemDetail,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        test_num_force_zero_test_program_list: &[String],
    ) -> Result<SubTestItemDetail, Box<dyn Error>> {
        self.common_service.fill_die_test_info(sub_test_item_detail, die_test_info_map, test_num_force_zero_test_program_list)
    }
}
