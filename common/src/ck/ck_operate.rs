use chrono::{DateTime, Utc};
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::time::{Duration, SystemTime};

use ck_provider::CkProviderImpl;
use ck_provider::{CkConfig, CkProvider};
use clickhouse::Row;
use lazy_static::lazy_static;
use log::{error, info, warn};
use serde::Deserialize;

/// CkOperate provides ClickHouse operations for data warehouse
/// This corresponds to the Java CkOperate class
pub struct CkOperate;
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct WaferIdAndFileIdDto {
    pub wafer_id: String,
    pub file_id: Option<i64>,
}

impl WaferIdAndFileIdDto {
    pub fn new(wafer_id: String, file_id: Option<i64>) -> Self {
        Self { wafer_id, file_id }
    }
}

/// Constants from Java CkOperate class
const DROP_ALL_PARTITION_SQL: &str = "ALTER TABLE {TABLE} ON CLUSTER {CLUSTER} {ALL_PARTITION}";
const TOMBSTONE_ALL_PARTITION_SQL: &str = "ALTER TABLE {TABLE} UPDATE IS_DELETE=1 {IN_PARTITION} WHERE CUSTOMER='{CUSTOMER}' AND FACTORY='{FACTORY}' AND UPLOAD_TYPE='AUTO' AND DEVICE_ID='{DEVICE_ID}' AND TEST_AREA='{TEST_AREA}' AND TEST_STAGE='{TEST_STAGE}' AND LOT_TYPE='{LOT_TYPE}' AND LOT_ID='{LOT_ID}' AND CREATE_TIME < toDateTime('{CURRENT_TIME}') {EXTRA_CONDITION} settings mutations_sync = 0";
const PARTITION_EXPR_PREFIX: &str = "IN PARTITION ";
const CP_TOMBSTONE_CONDITION_TEMPLATE: &str = " AND WAFER_NO='{WAFER_NO}'";
const WAT_TOMBSTONE_CONDITION_TEMPLATE: &str = " AND WAFER_NO IN ({WAFER_NO_LIST})";
const YMS_MES_TOMBSTONE_CONDITION_TEMPLATE: &str = " AND DATA_SOURCE='MES'";
const YMS_NOT_MES_TOMBSTONE_CONDITION_TEMPLATE: &str = " AND DATA_SOURCE!='MES'";
const LOT_BUCKET_CONDITION: &str = "{LOT_BUCKET_CONDITION}";
const LOT_BUCKET_CONDITION_TEMPLATE: &str = "AND LOT_BUCKET='{LOT_BUCKET}'";
const CLUSTER_TABLE: &str = "_cluster";
const LOCAL_TABLE: &str = "_local";
const SINGLE_DROP_PARTITION_TEMPLATE: &str = "DROP PARTITION {PARTITION}";
const REPLACE_ALL_PARTITION_SQL: &str = "ALTER TABLE {TABLE} ON CLUSTER {CLUSTER} {ALL_PARTITION}";
const SINGLE_REPLACE_PARTITION_TEMPLATE: &str = "REPLACE PARTITION {PARTITION} FROM {REPLACE_TABLE}";
const DWD_WAFER_PARTITION_TEMPLATE: &str =
    "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{LOT_ID}','{WAFER_ID}',{FILE_ID})";
const DWS_WAFER_PARTITION_TEMPLATE: &str = "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{LOT_ID}','{WAFER_ID}')";
const ADS_WAFER_PARTITION_TEMPLATE: &str = "('{CUSTOMER}','AUTO','{TEST_AREA}','{FACTORY}','{LOT_ID}','{WAFER_ID}')";
const CP_COUNT_SQL: &str = "SELECT COUNT(1) AS CNT FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' AND WAFER_NO='{WAFER_NO}'";
const CP_COUNT_NOT_DELETE_SQL: &str = "SELECT COUNT(1) AS CNT, SUB_CUSTOMER FROM (SELECT SUB_CUSTOMER FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND DEVICE_ID='{DEVICE_ID}' AND TEST_STAGE='{TEST_STAGE}' AND LOT_TYPE='{LOT_TYPE}' {LOT_BUCKET_CONDITION} AND LOT_ID='{LOT_ID}' AND WAFER_NO='{WAFER_NO}' {EXTRA_CONDITION} AND IS_DELETE=0 limit 1) GROUP BY SUB_CUSTOMER";
const FT_COUNT_SQL: &str = "SELECT COUNT(1) AS CNT FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}'";
const FT_COUNT_NOT_DELETE_SQL: &str = "SELECT COUNT(1) AS CNT, SUB_CUSTOMER FROM (SELECT SUB_CUSTOMER FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND DEVICE_ID='{DEVICE_ID}' AND TEST_STAGE='{TEST_STAGE}' AND LOT_TYPE='{LOT_TYPE}' {LOT_BUCKET_CONDITION} AND LOT_ID='{LOT_ID}' {EXTRA_CONDITION}  AND IS_DELETE=0 limit 1) GROUP BY SUB_CUSTOMER";
const WAT_COUNT_NOT_DELETE_SQL: &str = "SELECT COUNT(1) AS CNT, SUB_CUSTOMER FROM (SELECT SUB_CUSTOMER FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND DEVICE_ID='{DEVICE_ID}' AND TEST_STAGE='{TEST_STAGE}' AND LOT_TYPE='{LOT_TYPE}' {LOT_BUCKET_CONDITION} AND LOT_ID='{LOT_ID}' {EXTRA_CONDITION} AND WAFER_NO IN ({WAFER_NO_LIST}) AND IS_DELETE=0 limit 1) GROUP BY SUB_CUSTOMER";
const CP_DWD_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,FILE_ID AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' AND WAFER_NO='{WAFER_NO}' GROUP BY WAFER_ID,FILE_ID";
const CP_DWS_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,NULL AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' AND WAFER_NO='{WAFER_NO}' GROUP BY WAFER_ID";
const CP_ADS_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,NULL AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' AND WAFER_NO='{WAFER_NO}' GROUP BY WAFER_ID";
const FT_DWD_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,FILE_ID AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' GROUP BY WAFER_ID,FILE_ID";
const DWD_ALL_WAFERNO_SQL: &str = "SELECT WAFER_NO FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' GROUP BY WAFER_NO";
const FT_DWS_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,NULL AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' GROUP BY WAFER_ID";
const FT_ADS_ALL_WAFERID_SQL: &str = "SELECT WAFER_ID AS WAFER_ID,NULL AS FILE_ID FROM {TABLE} WHERE CUSTOMER='{CUSTOMER}' AND UPLOAD_TYPE='AUTO' AND TEST_AREA='{TEST_AREA}' AND FACTORY='{FACTORY}' AND LOT_ID='{LOT_ID}' GROUP BY WAFER_ID";
const TABLE: &str = "{TABLE}";
const WAFER_NO_LIST: &str = "{WAFER_NO_LIST}";
const REPLACE_TABLE: &str = "{REPLACE_TABLE}";
const EXTRA_CONDITION: &str = "{EXTRA_CONDITION}";
const IN_PARTITION: &str = "{IN_PARTITION}";
const PARTITION: &str = "{PARTITION}";
const ALL_PARTITION: &str = "{ALL_PARTITION}";
const CUSTOMER: &str = "{CUSTOMER}";
const SUB_CUSTOMER: &str = "{SUB_CUSTOMER}";
const TEST_AREA: &str = "{TEST_AREA}";
const FACTORY: &str = "{FACTORY}";
const DEVICE_ID: &str = "{DEVICE_ID}";
const LOT_BUCKET: &str = "{LOT_BUCKET}";
const LOT_ID_TO_LOT_BUCKET: &str = "abs(javaHash('{LOT_ID}')) % 6";
const LOT_ID: &str = "{LOT_ID}";
const LOT_TYPE: &str = "{LOT_TYPE}";
const TEST_STAGE: &str = "{TEST_STAGE}";
const WAFER_ID: &str = "{WAFER_ID}";
const CLUSTER: &str = "{CLUSTER}";
const WAFER_NO: &str = "{WAFER_NO}";
const FILE_ID: &str = "{FILE_ID}";
const UPLOAD_TYPE: &str = "{UPLOAD_TYPE}";
const CURRENT_TIME: &str = "{CURRENT_TIME}";
const CP_EQUALS_COUNT_SQL: &str = r#"SELECT CNT AS CNT
FROM (
         SELECT COUNT(1) AS CNT
         FROM {TABLE}
         WHERE CUSTOMER = '{CUSTOMER}'
           AND UPLOAD_TYPE = 'AUTO'
           AND TEST_AREA = '{TEST_AREA}'
           AND FACTORY = '{FACTORY}'
           AND LOT_ID = '{LOT_ID}'
           AND WAFER_NO = '{WAFER_NO}'
         ) a
         JOIN (
    SELECT COUNT(1) AS CNT
    FROM {REPLACE_TABLE}
    WHERE CUSTOMER = '{CUSTOMER}'
      AND UPLOAD_TYPE = 'AUTO'
      AND TEST_AREA = '{TEST_AREA}'
      AND FACTORY = '{FACTORY}'
      AND LOT_ID = '{LOT_ID}'
      AND WAFER_NO = '{WAFER_NO}'
    ) b
              using CNT;"#;

const FT_EQUALS_COUNT_SQL: &str = r#"SELECT CNT AS CNT
FROM (
         SELECT COUNT(1) AS CNT
         FROM {TABLE}
         WHERE CUSTOMER = '{CUSTOMER}'
           AND UPLOAD_TYPE = 'AUTO'
           AND TEST_AREA = '{TEST_AREA}'
           AND FACTORY = '{FACTORY}'
           AND LOT_ID = '{LOT_ID}'
         ) a
         JOIN (
    SELECT COUNT(1) AS CNT
    FROM {REPLACE_TABLE}
    WHERE CUSTOMER = '{CUSTOMER}'
      AND UPLOAD_TYPE = 'AUTO'
      AND TEST_AREA = '{TEST_AREA}'
      AND FACTORY = '{FACTORY}'
      AND LOT_ID = '{LOT_ID}'
    ) b
              using CNT;"#;
// Test area constants
const CP_TEST_AREAS: [&str; 13] = ["CP", "CP(Map)", "CP(InklessMap)", "BUMP", "BUMP(Map)", "BURNIN", "BURNIN(Map)", "WLT", "WLT(Map)", "REL(Wafer)", "REL(Map)", "ASSY(Wafer)", "ASSY(Map)"];
const WAT_TEST_AREA: &str = "WAT";
const OTHER_TEST_AREA: &str = "OTHER";

// Layer constants
const DWD_LAYER: &str = "DWD";
const DWS_LAYER: &str = "DWS";
const ADS_LAYER: &str = "ADS";

// Static maps for SQL queries and partition templates
lazy_static! {
    /// Maps layer and test area to SQL queries for retrieving wafer IDs and file IDs
    /// Corresponds to ALL_WAFERID_FILEID_SQL_MAP in Java CkOperate class
    static ref ALL_WAFERID_FILEID_SQL_MAP: HashMap<String, HashMap<String, String>> = {
        let mut map = HashMap::new();

        // DWD layer
        let mut dwd_map = HashMap::new();
        for area in &CP_TEST_AREAS {
            dwd_map.insert(area.to_string(), CP_DWD_ALL_WAFERID_SQL.to_string());
        }
        dwd_map.insert(OTHER_TEST_AREA.to_string(), FT_DWD_ALL_WAFERID_SQL.to_string());
        map.insert(DWD_LAYER.to_string(), dwd_map);

        // DWS layer
        let mut dws_map = HashMap::new();
        for area in &CP_TEST_AREAS {
            dws_map.insert(area.to_string(), CP_DWS_ALL_WAFERID_SQL.to_string());
        }
        dws_map.insert(OTHER_TEST_AREA.to_string(), FT_DWS_ALL_WAFERID_SQL.to_string());
        map.insert(DWS_LAYER.to_string(), dws_map);

        // ADS layer
        let mut ads_map = HashMap::new();
        for area in &CP_TEST_AREAS {
            ads_map.insert(area.to_string(), CP_ADS_ALL_WAFERID_SQL.to_string());
        }
        ads_map.insert(OTHER_TEST_AREA.to_string(), FT_ADS_ALL_WAFERID_SQL.to_string());
        map.insert(ADS_LAYER.to_string(), ads_map);

        map
    };

    /// Maps layer to partition templates
    /// Corresponds to PARTITION_TEMPLATE_MAP in Java CkOperate class
    static ref PARTITION_TEMPLATE_MAP: HashMap<String, String> = {
        let mut map = HashMap::new();
        map.insert(DWD_LAYER.to_string(), DWD_WAFER_PARTITION_TEMPLATE.to_string());
        map.insert(DWS_LAYER.to_string(), DWS_WAFER_PARTITION_TEMPLATE.to_string());
        map.insert(ADS_LAYER.to_string(), ADS_WAFER_PARTITION_TEMPLATE.to_string());
        map
    };
}

// Row types for ClickHouse queries
#[derive(Row, Deserialize, Debug)]
struct CountResult {
    cnt: i64,
}

#[derive(Row, Deserialize, Debug)]
struct CountSubCustomerResult {
    cnt: i64,
    sub_customer: String,
}

#[derive(Row, Deserialize, Debug)]
struct WaferIdFileIdResult {
    wafer_id: String,
    file_id: Option<i64>,
}

#[derive(Row, Deserialize, Debug)]
struct WaferNoResult {
    wafer_no: String,
}

impl CkOperate {
    /// Execute tombstone operation on ClickHouse table
    ///
    /// Corresponds to: CkOperate.tombstoneCk method in Java
    pub async fn tombstone_ck(
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        lot_bucket: Option<i32>, // Changed from i32 to Option<i32> to match Java implementation
        wafer_no: &str,
        table_full_name: &str, // dbName.tableName
        ck_address: &str,
        ck_address_list: &[String],
        ck_username: &str,
        ck_password: &str,
        partition_expr: &str,
        current_time: Option<DateTime<Utc>>,
    ) -> Result<(), Box<dyn Error>> {
        // Create a list of table and test area pairs for checking if tombstone is needed
        let table_and_test_area_list =
            vec![(table_full_name.to_string(), (test_area.to_string(), wafer_no.to_string()))];

        // Create a HashSet to collect sub_customer values
        let mut sub_customer_set = HashSet::new();

        // Check if tombstone is needed (if there are records with IS_DELETE=0)
        if !Self::is_tombstone(
            ck_address,
            ck_username,
            ck_password,
            &table_and_test_area_list,
            customer,
            factory,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            lot_bucket,
            &mut sub_customer_set,
            false,
            false,
        )
        .await?
        {
            info!(
                "开始tombstone {} 数据 lotId: {} , waferNo: {}, tableName: {}",
                test_area, lot_id, wafer_no, table_full_name
            );

            // Determine extra condition based on test area
            let extra_condition = if CP_TEST_AREAS.contains(&test_area) {
                CP_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO, wafer_no)
            } else if test_area == WAT_TEST_AREA {
                let wafer_no_list = wafer_no.split(',').map(|w| format!("'{}'", w)).collect::<Vec<_>>().join(",");
                WAT_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO_LIST, &wafer_no_list)
            } else {
                String::new()
            };

            let table_and_condition_list = vec![(table_full_name.to_string(), extra_condition)];

            info!("subCustomerSet: {:?}", sub_customer_set);

            // For each sub_customer, execute tombstone operation
            for sub_customer in sub_customer_set {
                match Self::tombstone(
                    ck_address_list,
                    ck_username,
                    ck_password,
                    partition_expr,
                    customer,
                    &sub_customer,
                    factory,
                    test_area,
                    lot_id,
                    lot_bucket,
                    device_id,
                    lot_type,
                    test_stage,
                    &table_and_condition_list,
                    current_time,
                )
                .await
                {
                    Ok(_) => {}
                    Err(e) => {
                        error!("Tombstone operation failed for sub_customer {}: {}", sub_customer, e);
                        return Err(e);
                    }
                }
            }
        } else {
            info!("不需要tombstone {} 数据 lotId: {} , tableName: {}", test_area, lot_id, table_full_name);
        }

        Ok(())
    }

    pub async fn tombstone_ck_not_check(
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        lot_bucket: Option<i32>, // Changed from i32 to Option<i32> to match Java implementation
        wafer_no: &str,
        table_full_name: &str, // dbName.tableName
        _ck_address: &str,
        ck_address_list: &[String],
        ck_username: &str,
        ck_password: &str,
        partition_expr: &str,
        current_time: Option<DateTime<Utc>>,
        sub_customer: &str,
    ) -> Result<(), Box<dyn Error>> {
        info!(
            "开始tombstone {} 数据 lotId: {} , waferNo: {}, tableName: {}",
            test_area, lot_id, wafer_no, table_full_name
        );

        // Determine extra condition based on test area
        let extra_condition = if CP_TEST_AREAS.contains(&test_area) {
            CP_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO, wafer_no)
        } else if test_area == WAT_TEST_AREA {
            let wafer_no_list = wafer_no.split(',').map(|w| format!("'{}'", w)).collect::<Vec<_>>().join(",");
            WAT_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO_LIST, &wafer_no_list)
        } else {
            String::new()
        };

        let table_and_condition_list = vec![(table_full_name.to_string(), extra_condition)];

        // For each sub_customer, execute tombstone operation
        match Self::tombstone(
            ck_address_list,
            ck_username,
            ck_password,
            partition_expr,
            customer,
            sub_customer,
            factory,
            test_area,
            lot_id,
            lot_bucket,
            device_id,
            lot_type,
            test_stage,
            &table_and_condition_list,
            current_time,
        )
        .await
        {
            Ok(_) => {}
            Err(e) => {
                error!("Tombstone operation failed for sub_customer {}: {}", sub_customer, e);
                return Err(e);
            }
        }
    
        Ok(())
    }

    /// Execute tombstone operation on ClickHouse table with YMS data source filtering
    ///
    /// Corresponds to: CkOperate.tombstoneCk overload with isYms and isMes parameters
    pub async fn tombstone_ck_yms(
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        lot_bucket: Option<i32>, // Changed from i32 to Option<i32> to match Java implementation
        wafer_no: &str,
        is_yms: bool,
        is_mes: bool,
        table_full_name: &str, // dbName.tableName
        ck_address: &str,
        ck_address_list: &[String],
        ck_username: &str,
        ck_password: &str,
        partition_expr: &str,
        current_time: Option<DateTime<Utc>>,
    ) -> Result<(), Box<dyn Error>> {
        // Create a list of table and test area pairs for checking if tombstone is needed
        let table_and_test_area_list =
            vec![(table_full_name.to_string(), (test_area.to_string(), wafer_no.to_string()))];

        // Create a HashSet to collect sub_customer values
        let mut sub_customer_set = HashSet::new();

        // Check if tombstone is needed (if there are records with IS_DELETE=0)
        if !Self::is_tombstone(
            ck_address,
            ck_username,
            ck_password,
            &table_and_test_area_list,
            customer,
            factory,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            lot_bucket,
            &mut sub_customer_set,
            is_yms,
            is_mes,
        )
        .await?
        {
            info!(
                "开始tombstone {} 数据 lotId: {} , waferNo: {}, tableName: {}",
                test_area, lot_id, wafer_no, table_full_name
            );

            // Determine extra condition based on test area
            let mut extra_condition = if CP_TEST_AREAS.contains(&test_area) {
                CP_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO, wafer_no)
            } else if test_area == WAT_TEST_AREA {
                let wafer_no_list = wafer_no.split(',').map(|w| format!("'{}'", w)).collect::<Vec<_>>().join(",");
                WAT_TOMBSTONE_CONDITION_TEMPLATE.replace(WAFER_NO_LIST, &wafer_no_list)
            } else {
                String::new()
            };

            // Add YMS data source condition if needed
            if is_yms {
                extra_condition.push_str(if is_mes {
                    YMS_MES_TOMBSTONE_CONDITION_TEMPLATE
                } else {
                    YMS_NOT_MES_TOMBSTONE_CONDITION_TEMPLATE
                });
            }

            let table_and_condition_list = vec![(table_full_name.to_string(), extra_condition)];

            info!("subCustomerSet: {:?}", sub_customer_set);

            // For each sub_customer, execute tombstone operation
            for sub_customer in sub_customer_set {
                match Self::tombstone(
                    ck_address_list,
                    ck_username,
                    ck_password,
                    partition_expr,
                    customer,
                    &sub_customer,
                    factory,
                    test_area,
                    lot_id,
                    lot_bucket,
                    device_id,
                    lot_type,
                    test_stage,
                    &table_and_condition_list,
                    current_time,
                )
                .await
                {
                    Ok(_) => {}
                    Err(e) => {
                        error!("Tombstone operation failed for sub_customer {}: {}", sub_customer, e);
                        return Err(e);
                    }
                }
            }
        } else {
            info!("不需要tombstone {} 数据 lotId: {} , tableName: {}", test_area, lot_id, table_full_name);
        }

        Ok(())
    }

    /// Check if data is deleted from ClickHouse
    ///
    /// Corresponds to: CkOperate.isDeleted method in Java
    pub async fn is_deleted(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        table_and_test_area_and_wafer_no_list: &[(String, (String, String))],
        customer: &str,
        factory: &str,
        lot_id: &str,
    ) -> Result<bool, Box<dyn Error>> {
        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Check each table and test area
        for (table, (test_area, wafer_no)) in table_and_test_area_and_wafer_no_list {
            // Determine which SQL to use based on test area
            let sql = if CP_TEST_AREAS.contains(&test_area.as_str()) {
                CP_COUNT_SQL
                    .replace(TABLE, table)
                    .replace(CUSTOMER, customer)
                    .replace(TEST_AREA, test_area)
                    .replace(FACTORY, factory)
                    .replace(LOT_ID, lot_id)
                    .replace(WAFER_NO, wafer_no)
            } else {
                FT_COUNT_SQL
                    .replace(TABLE, table)
                    .replace(CUSTOMER, customer)
                    .replace(TEST_AREA, test_area)
                    .replace(FACTORY, factory)
                    .replace(LOT_ID, lot_id)
            };

            info!("isDeleted {} 开始", sql);
            let start_time = SystemTime::now();

            // Execute query
            let result: Vec<CountResult> = client.query(&sql).await?;

            if !result.is_empty() {
                let cnt = result[0].cnt;
                info!("isDeleted {} 结束,cnt {}, 用时 {} ms", sql, cnt, start_time.elapsed()?.as_millis());

                // If count > 0, data is not deleted
                if cnt > 0 {
                    return Ok(false);
                }
            }
        }

        // All tables have count = 0, data is deleted
        Ok(true)
    }

    /// Check if data is tombstoned (marked as deleted) in ClickHouse
    ///
    /// Corresponds to: CkOperate.isTombstone method in Java
    pub async fn is_tombstone(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        table_and_test_area_and_wafer_no_list: &[(String, (String, String))],
        customer: &str,
        factory: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        lot_bucket: Option<i32>,
        sub_customer_set: &mut HashSet<String>,
        is_yms: bool,
        is_mes: bool,
    ) -> Result<bool, Box<dyn Error>> {
        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Check each table and test area
        for (table, (test_area, wafer_no)) in table_and_test_area_and_wafer_no_list {
            // Determine which SQL template to use based on test area
            let sql_template = if CP_TEST_AREAS.contains(&test_area.as_str()) {
                CP_COUNT_NOT_DELETE_SQL.replace(WAFER_NO, wafer_no)
            } else if test_area == WAT_TEST_AREA {
                let wafer_no_list = wafer_no.split(',').map(|w| format!("'{}'", w)).collect::<Vec<_>>().join(",");
                WAT_COUNT_NOT_DELETE_SQL.replace(WAFER_NO_LIST, &wafer_no_list)
            } else {
                FT_COUNT_NOT_DELETE_SQL.to_string()
            };

            // Add extra condition for YMS data
            let extra_condition = if is_yms {
                if is_mes {
                    YMS_MES_TOMBSTONE_CONDITION_TEMPLATE.to_string()
                } else {
                    YMS_NOT_MES_TOMBSTONE_CONDITION_TEMPLATE.to_string()
                }
            } else {
                String::new()
            };

            // Add lot bucket condition if provided
            // If lot_bucket is provided, use it; otherwise use empty string (no lot bucket condition)
            // This matches the Java implementation's behavior
            let lot_bucket_condition = if let Some(bucket) = lot_bucket {
                LOT_BUCKET_CONDITION_TEMPLATE.replace(LOT_BUCKET, &bucket.to_string())
            } else {
                String::new()
            };

            // Build the final SQL
            let sql = sql_template
                .replace(TABLE, &table.replace(LOCAL_TABLE, CLUSTER_TABLE))
                .replace(CUSTOMER, customer)
                .replace(TEST_AREA, test_area)
                .replace(FACTORY, factory)
                .replace(DEVICE_ID, device_id)
                .replace(LOT_BUCKET_CONDITION, &lot_bucket_condition)
                .replace(LOT_ID, lot_id)
                .replace(LOT_TYPE, lot_type)
                .replace(TEST_STAGE, test_stage)
                .replace(EXTRA_CONDITION, &extra_condition);

            info!("isTombstone {} 开始", sql);
            let start_time = SystemTime::now();

            // Execute query
            let result: Vec<CountSubCustomerResult> = client.query(&sql).await?;

            let mut cnt = 0;
            for row in result {
                cnt += row.cnt;
                sub_customer_set.insert(row.sub_customer);
                info!("cnt = {}, subCustomerSet = {:?}", cnt, sub_customer_set);
            }

            info!("isTombstone {} 结束,cnt {}, 用时 {} ms", sql, cnt, start_time.elapsed()?.as_millis());

            // If count > 0, there are non-tombstoned records
            if cnt > 0 {
                return Ok(false);
            }
        }

        // All tables have count = 0, all data is tombstoned
        Ok(true)
    }

    /// Execute tombstone operation on ClickHouse tables
    ///
    /// Corresponds to: CkOperate.tombstone method in Java
    pub async fn tombstone(
        ck_address_list: &[String],
        ck_username: &str,
        ck_password: &str,
        partition_expr: &str,
        customer: &str,
        sub_customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_bucket: Option<i32>,
        device_id: &str,
        lot_type: &str,
        test_stage: &str,
        table_and_condition_list: &[(String, String)],
        current_time: Option<DateTime<Utc>>,
    ) -> Result<(), Box<dyn Error>> {
        // Build partition expression
        let mut in_partition = format!("{}{}", PARTITION_EXPR_PREFIX, partition_expr)
            .replace(UPLOAD_TYPE, "AUTO")
            .replace(CUSTOMER, customer)
            .replace(SUB_CUSTOMER, sub_customer)
            .replace(FACTORY, factory)
            .replace(TEST_AREA, test_area)
            .replace(LOT_ID, lot_id)
            .replace(DEVICE_ID, device_id)
            .replace(LOT_TYPE, lot_type)
            .replace(TEST_STAGE, test_stage);

        // Handle lot bucket
        // If lot_bucket is provided, use it; otherwise calculate it using the LOT_ID_TO_LOT_BUCKET formula
        // This matches the Java implementation's behavior
        if let Some(bucket) = lot_bucket {
            in_partition = in_partition.replace(LOT_BUCKET, &bucket.to_string());
        } else {
            in_partition = in_partition.replace(LOT_BUCKET, &LOT_ID_TO_LOT_BUCKET.replace(LOT_ID, lot_id));
        }

        // Check if partition expression is valid
        if in_partition.contains('{') {
            return Err(format!("存在不支持的分区键: {}", in_partition).into());
        }

        // If partition expression is just the prefix, don't use it
        let final_in_partition = if in_partition == PARTITION_EXPR_PREFIX {
            warn!("未使用in partition进行tombstone，需要检查handler中是否重写了partitionExpr！");
            String::new()
        } else {
            in_partition
        };

        // Get current timestamp in seconds
        let current_time = current_time.unwrap_or(Utc::now());

        // Execute tombstone on each ClickHouse node in parallel
        let mut tasks = Vec::new();

        for address in ck_address_list {
            let address = address.clone();
            let username = ck_username.to_string();
            let password = ck_password.to_string();
            let final_in_partition = final_in_partition.clone();
            let table_and_condition_list = table_and_condition_list.to_vec();
            let customer = customer.to_string();
            let factory = factory.to_string();
            let test_area = test_area.to_string();
            let lot_id = lot_id.to_string();
            let device_id = device_id.to_string();
            let lot_type = lot_type.to_string();
            let test_stage = test_stage.to_string();

            // Create async task for each ClickHouse node
            let task = tokio::spawn(async move {
                // Create ClickHouse client
                let client = match Self::get_provider(&address, &username, &password) {
                    Ok(client) => client,
                    Err(e) => {
                        error!("Failed to create ClickHouse client for {}: {}", address, e);
                        return Err(format!("Failed to create ClickHouse client: {}", e));
                    }
                };

                // Execute tombstone on each table
                for (table, extra_condition) in table_and_condition_list {
                    // Build tombstone SQL
                    let sql = TOMBSTONE_ALL_PARTITION_SQL
                        .replace(TABLE, &table.replace(CLUSTER_TABLE, LOCAL_TABLE))
                        .replace(CUSTOMER, &customer)
                        .replace(FACTORY, &factory)
                        .replace(TEST_AREA, &test_area)
                        .replace(LOT_ID, &lot_id)
                        .replace(DEVICE_ID, &device_id)
                        .replace(LOT_TYPE, &lot_type)
                        .replace(TEST_STAGE, &test_stage)
                        .replace(EXTRA_CONDITION, &extra_condition)
                        .replace(IN_PARTITION, &final_in_partition)
                        .replace(CURRENT_TIME, &current_time.timestamp().to_string());

                    let start_time = SystemTime::now();
                    info!("tombstone {} {} 开始", address, sql);

                    // Execute SQL
                    if let Err(e) = client.execute(&sql).await {
                        error!("Failed to execute tombstone SQL: {}", e);
                        return Err(format!("Failed to execute tombstone SQL: {}", e));
                    }

                    info!(
                        "tombstone {} {} 结束，用时 {} ms",
                        address,
                        sql,
                        start_time.elapsed().unwrap_or_default().as_millis()
                    );
                }

                Ok(())
            });

            tasks.push(task);
        }

        // Wait for all tasks to complete
        for task in tasks {
            match task.await {
                Ok(result) => {
                    if let Err(e) = result {
                        return Err(format!("Tombstone operation failed: {}", e).into());
                    }
                }
                Err(e) => {
                    return Err(format!("Tombstone task failed: {}", e).into());
                }
            }
        }

        Ok(())
    }

    /// Get all wafer IDs and file IDs from ClickHouse
    ///
    /// Corresponds to: CkOperate.getAllWaferIdAndFileIds method in Java
    pub async fn get_all_wafer_id_and_file_ids(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        count_table_list: &[String],
        customer: &str,
        test_area: &str,
        factory: &str,
        lot_id: &str,
        wafer_no: &str,
        dw_layer: &str,
    ) -> Result<HashSet<WaferIdAndFileIdDto>, Box<dyn Error>> {
        let mut wafer_id_and_file_ids = HashSet::new();

        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Query each table
        for table in count_table_list {
            // Determine which SQL to use based on test area and layer using the static map
            // This matches the Java implementation's behavior
            let sql = if let Some(layer_map) = ALL_WAFERID_FILEID_SQL_MAP.get(dw_layer) {
                // First try to get SQL for the specific test area
                if let Some(sql) = layer_map.get(test_area) {
                    sql
                }
                // If not found, try to use the OTHER_TEST_AREA SQL
                else if let Some(sql) = layer_map.get(OTHER_TEST_AREA) {
                    sql
                }
                // If still not found, return an error
                else {
                    error!("Unsupported test area: {}", test_area);
                    return Err(format!("Unsupported test area: {}", test_area).into());
                }
            } else {
                error!("Unsupported DW layer: {}", dw_layer);
                return Err(format!("Unsupported DW layer: {}", dw_layer).into());
            };

            // Build the final SQL
            let sql = sql
                .replace(TABLE, table)
                .replace(CUSTOMER, customer)
                .replace(TEST_AREA, test_area)
                .replace(FACTORY, factory)
                .replace(LOT_ID, lot_id)
                .replace(WAFER_NO, wafer_no);

            info!("getAllWaferIdAndFileIds {} 开始", sql);

            // Execute query
            let result: Vec<WaferIdFileIdResult> = match client.query(&sql).await {
                Ok(result) => result,
                Err(e) => {
                    error!("Failed to execute query: {}", e);
                    return Err(Box::new(e));
                }
            };

            // Add results to set
            for row in result {
                let dto = WaferIdAndFileIdDto { wafer_id: row.wafer_id, file_id: row.file_id };
                wafer_id_and_file_ids.insert(dto);
            }

            for dto in &wafer_id_and_file_ids {
                info!("getAllWaferIds res:{:?}", dto);
            }

            info!("getAllWaferIdAndFileIds {} 结束", sql);
        }

        Ok(wafer_id_and_file_ids)
    }

    /// Get all wafer numbers from DWD layer
    ///
    /// Corresponds to: CkOperate.getDwdAllWaferNo method in Java
    pub async fn get_dwd_all_wafer_no(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        delete_table_list: &[String],
        customer: &str,
        test_area: &str,
        factory: &str,
        lot_id: &str,
    ) -> Result<Vec<String>, Box<dyn Error>> {
        let mut wafer_no_set = HashSet::new();

        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Query each table
        for table in delete_table_list {
            // Build SQL
            let sql = DWD_ALL_WAFERNO_SQL
                .replace(TABLE, table)
                .replace(CUSTOMER, customer)
                .replace(TEST_AREA, test_area)
                .replace(FACTORY, factory)
                .replace(LOT_ID, lot_id);

            info!("getAllWaferNo {} 开始", sql);

            // Execute query
            let result: Vec<WaferNoResult> = match client.query(&sql).await {
                Ok(result) => result,
                Err(e) => {
                    error!("Failed to execute query: {}", e);
                    return Err(Box::new(e));
                }
            };

            // Add results to set
            for row in result {
                wafer_no_set.insert(row.wafer_no);
            }

            for wafer_no in &wafer_no_set {
                info!("getAllWaferNo res:{}", wafer_no);
            }

            info!("getAllWaferNo {} 结束", sql);
        }

        // Convert HashSet to Vec and return
        Ok(wafer_no_set.into_iter().collect())
    }

    /// Replace partitions in ClickHouse tables
    ///
    /// Corresponds to: CkOperate.replacePartitions method in Java
    pub async fn replace_partitions(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        cluster_table_list: &[(String, String)],
        local_table_list: &[(String, String)],
        customer: &str,
        test_area: &str,
        factory: &str,
        lot_id: &str,
        wafer_no: &str,
        cluster: &str,
        dw_layer: &str,
        max_check_count: i32,
    ) -> Result<(), Box<dyn Error>> {
        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Get all wafer IDs and file IDs from replace tables
        let mut wafer_id_and_file_ids = HashSet::new();

        for (_, replace_table) in cluster_table_list {
            // Determine which SQL to use based on test area and layer
            let sql = match dw_layer {
                DWD_LAYER => {
                    if CP_TEST_AREAS.contains(&test_area) {
                        CP_DWD_ALL_WAFERID_SQL
                    } else {
                        FT_DWD_ALL_WAFERID_SQL
                    }
                }
                DWS_LAYER => {
                    if CP_TEST_AREAS.contains(&test_area) {
                        CP_DWS_ALL_WAFERID_SQL
                    } else {
                        FT_DWS_ALL_WAFERID_SQL
                    }
                }
                ADS_LAYER => {
                    if CP_TEST_AREAS.contains(&test_area) {
                        CP_ADS_ALL_WAFERID_SQL
                    } else {
                        FT_ADS_ALL_WAFERID_SQL
                    }
                }
                _ => return Err(format!("Unsupported DW layer: {}", dw_layer).into()),
            };

            // Build the final SQL
            let sql = sql
                .replace(TABLE, &replace_table)
                .replace(CUSTOMER, customer)
                .replace(TEST_AREA, test_area)
                .replace(FACTORY, factory)
                .replace(LOT_ID, lot_id)
                .replace(WAFER_NO, wafer_no);

            info!("getAllWaferIdAndFileIds {} 开始", sql);

            // Execute query
            let result: Vec<WaferIdFileIdResult> = client.query(&sql).await?;

            // Add results to set
            for row in result {
                let dto = WaferIdAndFileIdDto { wafer_id: row.wafer_id, file_id: row.file_id };
                wafer_id_and_file_ids.insert(dto);
            }

            for dto in &wafer_id_and_file_ids {
                info!("getAllWaferIds res:{:?}", dto);
            }

            info!("getAllWaferIdAndFileIds {} 结束", sql);
        }

        // If we found wafer IDs, proceed with replace
        if !wafer_id_and_file_ids.is_empty() {
            // Get partition template for the layer using the static map
            // This matches the Java implementation's behavior
            let partition_template = if let Some(template) = PARTITION_TEMPLATE_MAP.get(dw_layer) {
                template
            } else {
                error!("Unsupported DW layer: {}", dw_layer);
                return Err(format!("Unsupported DW layer: {}", dw_layer).into());
            };

            // Build partitions list
            let partitions: Vec<String> = wafer_id_and_file_ids
                .iter()
                .map(|dto| {
                    partition_template
                        .replace(CUSTOMER, customer)
                        .replace(TEST_AREA, test_area)
                        .replace(FACTORY, factory)
                        .replace(LOT_ID, lot_id)
                        .replace(WAFER_ID, &dto.wafer_id)
                        .replace(FILE_ID, &dto.file_id.map_or("null".to_string(), |id| id.to_string()))
                })
                .collect();

            // Build replace SQL for each table
            let mut replace_sqls = HashSet::new();

            for (table, replace_table) in local_table_list {
                let mut partition_clauses = Vec::new();

                for partition in &partitions {
                    partition_clauses.push(
                        SINGLE_REPLACE_PARTITION_TEMPLATE
                            .replace(PARTITION, partition)
                            .replace(REPLACE_TABLE, replace_table),
                    );
                }

                let replace_sql = REPLACE_ALL_PARTITION_SQL
                    .replace(TABLE, table)
                    .replace(CLUSTER, cluster)
                    .replace(ALL_PARTITION, &partition_clauses.join(","));

                replace_sqls.insert(replace_sql);
            }

            // Execute replace and check if successful
            let mut check_count = 1;
            let mut replace_success = false;

            while check_count <= max_check_count && !replace_success {
                // Execute replace SQL
                for replace_sql in &replace_sqls {
                    info!("replacePartition {} 开始", replace_sql);

                    if let Err(e) = client.execute(replace_sql).await {
                        error!("Failed to execute replace SQL: {}", e);
                        return Err(format!("Failed to execute replace SQL: {}", e).into());
                    }

                    info!("replacePartition {} 结束", replace_sql);
                }

                // Wait for replace to complete
                tokio::time::sleep(Duration::from_secs(10)).await;

                // Check if replace was successful
                replace_success = true;

                for (table, replace_table) in cluster_table_list {
                    // Build equals count SQL
                    let equals_count_sql = if CP_TEST_AREAS.contains(&test_area) {
                        CP_EQUALS_COUNT_SQL
                            .replace(TABLE, table)
                            .replace(REPLACE_TABLE, replace_table)
                            .replace(CUSTOMER, customer)
                            .replace(TEST_AREA, test_area)
                            .replace(FACTORY, factory)
                            .replace(LOT_ID, lot_id)
                            .replace(WAFER_NO, wafer_no)
                    } else {
                        FT_EQUALS_COUNT_SQL
                            .replace(TABLE, table)
                            .replace(REPLACE_TABLE, replace_table)
                            .replace(CUSTOMER, customer)
                            .replace(TEST_AREA, test_area)
                            .replace(FACTORY, factory)
                            .replace(LOT_ID, lot_id)
                    };

                    info!("equalsCountSql {} 开始", equals_count_sql);

                    // Execute query
                    let result: Vec<CountResult> = client.query(&equals_count_sql).await?;

                    let equals_flag = !result.is_empty();

                    if !equals_flag {
                        replace_success = false;
                        break;
                    }

                    for row in &result {
                        info!("equalsCountSql CNT:{} ", row.cnt);
                    }

                    info!("equalsCountSql {} 结束", equals_count_sql);
                }

                check_count += 1;
            }

            // If replace failed after max attempts, return error
            if !replace_success {
                return Err("与replace表数据不一致！".into());
            }

            // If replace succeeded, drop partitions from replace tables
            let mut drop_sqls = HashSet::new();

            for (_, replace_table) in local_table_list {
                let mut partition_clauses = Vec::new();

                for partition in &partitions {
                    partition_clauses.push(SINGLE_DROP_PARTITION_TEMPLATE.replace(PARTITION, partition));
                }

                let drop_sql = DROP_ALL_PARTITION_SQL
                    .replace(TABLE, replace_table)
                    .replace(CLUSTER, cluster)
                    .replace(ALL_PARTITION, &partition_clauses.join(","));

                drop_sqls.insert(drop_sql);
            }

            // Execute drop SQL
            for drop_sql in drop_sqls {
                info!("dropPartition {} 开始", drop_sql);

                if let Err(e) = client.execute(&drop_sql).await {
                    error!("Failed to execute drop SQL: {}", e);
                    return Err(Box::new(e));
                }

                info!("dropPartition {} 结束", drop_sql);
            }
        }

        Ok(())
    }

    /// Delete partitions from ClickHouse tables
    ///
    /// Corresponds to: CkOperate.deleteAllPartition method in Java
    pub async fn delete_all_partition(
        ck_address: &str,
        ck_username: &str,
        ck_password: &str,
        cluster: &str,
        table_and_all_partition_list: &[(String, String)],
    ) -> Result<(), Box<dyn Error>> {
        // Create ClickHouse client
        let client = Self::get_provider(ck_address, ck_username, ck_password)?;

        // Execute delete for each table and partition
        for (table, all_partition) in table_and_all_partition_list {
            // Build SQL
            let sql = DROP_ALL_PARTITION_SQL
                .replace(TABLE, table)
                .replace(CLUSTER, cluster)
                .replace(ALL_PARTITION, all_partition);

            info!("deletePartition {} 开始", sql);

            // Execute SQL
            if let Err(e) = client.execute(&sql).await {
                error!("Failed to execute delete SQL: {}", e);
                return Err(format!("Failed to execute delete SQL: {}", e).into());
            }

            info!("deletePartition {} 结束", sql);
        }

        Ok(())
    }

    /// Create ClickHouse client
    ///
    /// Corresponds to: CkOperate.getConnection method in Java
    ///
    /// This method creates a new ClickHouse client with the specified connection parameters.
    /// In the Java implementation, this method throws a RuntimeException if the connection fails.
    /// In the Rust implementation, we return a Result instead.
    ///
    /// # Arguments
    ///
    /// * `address` - The ClickHouse server address (e.g., "http://localhost:8123")
    /// * `username` - The ClickHouse username
    /// * `password` - The ClickHouse password
    ///
    /// # Returns
    ///
    /// A Result containing the ClickHouse client or an error
    fn get_provider(address: &str, username: &str, password: &str) -> Result<CkProviderImpl, Box<dyn Error>> {
        // 配置ClickHouse连接
        let config = CkConfig {
            url: address.to_string(),
            username: username.to_string(),
            password: password.to_string(),
            database: "default".to_string(),
            timeout: Duration::from_secs(30),
            batch_size: 1000,
            compression: true,
            ..Default::default() 
        };

        // 创建ClickHouse Provider
        let provider = CkProviderImpl::new(config);

        Ok(provider)
    }
}
