#!/bin/bash
version=$1
DATAWARE_VERSION=$2
if [ -z "$2" ]; then
    echo "Usage: $0 <version>"
    echo "Usage: $1 <DATAWARE_VERSION>"
    exit 1
fi
pwd=`pwd`
rm -rf $pwd/deploy/$version
mkdir -p $pwd/deploy/$version
CRATE_NAME="native"
echo "Crate Name: $CRATE_NAME"
echo "Target Version: $DATAWARE_VERSION"

echo "Building the native library with Cargo..."
cargo build --release

# 检查构建是否成功
if [ ! -f "target/release/lib${CRATE_NAME}.so" ]; then
    echo "Error: Build failed, library file not found"
    exit 1
fi

SOURCE_FILE="target/release/lib${CRATE_NAME}.so"
DEST_FILE="target/release/lib${CRATE_NAME}-${DATAWARE_VERSION}.so"

echo "Renaming ${SOURCE_FILE} to ${DEST_FILE}"
mv "${SOURCE_FILE}" "${DEST_FILE}"

cp "${DEST_FILE}" $pwd/deploy/$version
cp "upgrade.sh" $pwd/deploy/$version
cp "deploy.sh" $pwd/deploy/$version